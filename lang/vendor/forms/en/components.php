<?php

return [

    'builder' => [

        'collapsed' => 'Content collapsed',

        'buttons' => [

            'clone_item' => [
                'label' => 'Clone',
            ],

            'create_item' => [
                'label' => 'Add to :label',
            ],

            'create_item_between' => [
                'label' => 'Insert',
            ],

            'delete_item' => [
                'label' => 'Delete',
            ],

            'move_item' => [
                'label' => 'Move',
            ],

            'move_item_down' => [
                'label' => 'Move down',
            ],

            'move_item_up' => [
                'label' => 'Move up',
            ],

            'collapse_item' => [
                'label' => 'Collapse',
            ],

            'expand_item' => [
                'label' => 'Expand',
            ],

            'collapse_all' => [
                'label' => 'Collapse all',
            ],

            'expand_all' => [
                'label' => 'Expand all',
            ],

        ],

    ],

    'checkbox_list' => [

        'buttons' => [

            'deselect_all' => [
                'label' => 'Deselect all',
            ],

            'select_all' => [
                'label' => 'Select all',
            ],

        ],

    ],

    'key_value' => [

        'buttons' => [

            'add' => [
                'label' => 'Add row',
            ],

            'delete' => [
                'label' => 'Delete row',
            ],

            'reorder' => [
                'label' => 'Reorder row',
            ],

        ],

        'fields' => [

            'key' => [
                'label' => 'Key',
            ],

            'value' => [
                'label' => 'Value',
            ],

        ],

    ],

    'markdown_editor' => [

        'toolbar_buttons' => [
            'attach_files' => 'Attach files',
            'bold' => 'Bold',
            'bullet_list' => 'Bullet list',
            'code_block' => 'Code block',
            'edit' => 'Edit',
            'italic' => 'Italic',
            'link' => 'Link',
            'ordered_list' => 'Numbered list',
            'preview' => 'Preview',
            'strike' => 'Strikethrough',
        ],

    ],

    'repeater' => [

        'collapsed' => 'Content collapsed',

        'buttons' => [

            'create_item' => [
                'label' => 'Add to :label',
            ],

            'delete_item' => [
                'label' => 'Delete',
            ],

            'clone_item' => [
                'label' => 'Clone',
            ],

            'move_item' => [
                'label' => 'Move',
            ],

            'move_item_down' => [
                'label' => 'Move down',
            ],

            'move_item_up' => [
                'label' => 'Move up',
            ],

            'collapse_item' => [
                'label' => 'Collapse',
            ],

            'expand_item' => [
                'label' => 'Expand',
            ],

            'collapse_all' => [
                'label' => 'Collapse all',
            ],

            'expand_all' => [
                'label' => 'Expand all',
            ],

        ],

    ],

    'rich_editor' => [

        'dialogs' => [

            'link' => [

                'buttons' => [
                    'link' => 'Link',
                    'unlink' => 'Unlink',
                ],

                'label' => 'URL',

                'placeholder' => 'Enter a URL',

            ],

        ],

        'toolbar_buttons' => [
            'attach_files' => 'Attach files',
            'blockquote' => 'Blockquote',
            'bold' => 'Bold',
            'bullet_list' => 'Bullet list',
            'code_block' => 'Code block',
            'h1' => 'Title',
            'h2' => 'Heading',
            'h3' => 'Subheading',
            'italic' => 'Italic',
            'link' => 'Link',
            'ordered_list' => 'Numbered list',
            'redo' => 'Redo',
            'strike' => 'Strikethrough',
            'underline' => 'Underline',
            'undo' => 'Undo',
        ],

    ],

    'select' => [

        'actions' => [

            'create_option' => [

                'modal' => [

                    'heading' => 'Create',

                    'actions' => [

                        'create' => [
                            'label' => 'Create',
                        ],

                    ],

                ],

            ],

        ],

        'boolean' => [
            'true' => 'Yes',
            'false' => 'No',
        ],

        'loading_message' => 'Loading...',

        'max_items_message' => 'Only :count can be selected.',

        'no_search_results_message' => 'No options match your search.',

        'placeholder' => 'Select an option',

        'searching_message' => 'Searching...',

        'search_prompt' => 'Start typing to search...',

    ],

    'tags_input' => [
        'placeholder' => 'New tag',
    ],

    'wizard' => [

        'buttons' => [

            'previous_step' => [
                'label' => 'Back',
            ],

            'next_step' => [
                'label' => 'Next',
            ],

        ],

    ],

];
