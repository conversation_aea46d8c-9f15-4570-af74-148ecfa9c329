<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('custom_settings', function (Blueprint $table) {
            $table->id();
            $table->string('settings_key')->unique();
            $table->text('settings_value')->nullable();
            $table->text('settings_value_en')->nullable();
            $table->string('settings_file')->nullable();
            $table->string('settings_file_en')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('custom_settings');
    }
};
