<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Department extends Model
{
    use HasFactory;

    public $timestamps = false;
    protected $guarded = ["id"];
    protected $fillable = ["name", "firm_id", "name_en"];

    public function firm(): BelongsTo
    {
        return $this->belongsTo(Firm::class, 'firm_id', 'id');
    }

    public function members(): HasMany
    {
        return $this->hasMany(Member::class, 'department_id', 'id');
    }
}
