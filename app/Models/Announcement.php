<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Announcement extends Model
{
    use HasFactory;

    public $timestamps = false;
    protected $guarded = ["id"];

    public function getByBenefitId($benefitId)
    {
        $result = DB::table('benefits')
            ->join('benefit_groups', 'benefit_groups.id', '=', 'benefits.benefit_group_id')
            ->where('benefits.id', $benefitId)
            ->value('benefit_groups.announcement_id');

        return $result ? $result : 0;
    }
   
}
