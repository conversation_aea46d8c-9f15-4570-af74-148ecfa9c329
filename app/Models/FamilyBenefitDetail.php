<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class FamilyBenefitDetail extends Model
{
    use HasFactory;
    public $timestamps = false;
    protected $guarded = ["id"];
    protected $casts = [
        'relation' => 'array',
    ];

    public function familyBenefit(): HasMany
    {
        return $this->hasMany(FamilyBenefitDetail::class, 'benefit_group_id', 'id')->select('id');
    }

    public function benefits(): HasManyThrough
{
    return $this->hasManyThrough(
        Benefit::class, 
        BenefitGroup::class,
        'benefit_group_id', // BenefitGroup table foreign key
        'benefit_group_id', // Benefit table foreign key
        'id', // BenefitGroup table local key
        'id' // Benefit table local key
    )->select('benefit_groups.name');
}


}
