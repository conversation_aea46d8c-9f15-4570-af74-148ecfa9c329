<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Position extends Model
{
    use HasFactory;

    public $timestamps = false;
    protected $guarded = ["id"];
    protected $fillable = ["name","name_en"];

    public function members(): HasMany
    {
        return $this->hasMany(Member::class, 'position_id', 'id');
    }
}
