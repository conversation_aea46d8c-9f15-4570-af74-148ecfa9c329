<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class Question extends Model
{
    use HasFactory;

    public $timestamps = false;
    protected $guarded = ["id"];
    protected $fillable = ["text", "order_no", "status","text_en"];


    public function questionOptions(): HasMany
    {
        return $this->hasMany(QuestionOption::class, 'question_id', 'id');
    }

    public function getUnansweredQuestions($member_id)
    {
        return $this->leftJoin('question_answers as qa', function ($join) use ($member_id) {
                $join->on('qa.question_id', '=', 'questions.id')
                     ->where('qa.member_id', $member_id);
            })
            ->whereNull('qa.id')
            ->where('questions.status', 1)
            ->orderBy('questions.order_no', 'ASC')
            ->get(['questions.*']);
    }

    public function insertAnswer($data)
    {
        $data['date'] = Carbon::now()->toDateTimeString();
        return DB::table('question_answers')->updateOrInsert([
            'member_id' => $data['member_id'],
            'question_id' => $data['question_id']
        ], $data);
    }
}
