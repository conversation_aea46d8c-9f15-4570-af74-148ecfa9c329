<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class QuestionOption extends Model
{
    use HasFactory;

    public $timestamps = false;
    protected $guarded = ["id"];
    
    public function question(): BelongsTo
    {
        return $this->belongsTo(Question::class, 'question_id', 'id');
    }


    public function getOptions()
    {
        return QuestionOption::orderBy('order_no', 'ASC')->get();
    }

    public function insertOption($data)
    {
        return QuestionOption::updateOrCreate(
            ['question_id' => $data['question_id']], 
            $data 
        );
    }
   
}
