<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class StaticPage extends Model
{
    use HasFactory;
    
    public $timestamps = false;
    protected $guarded = ["id"];
    protected $attributes = [
        'item_id' => 0,
    ];
    
    public function setSeoAttribute($value)
    {
        $this->attributes['seo'] = empty($value) ? Str::slug($this->title) : $value;
    }
}
