<?php

namespace App\Models;
use App\Traits\FamilyBenefitTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Auth;
use App\Models\Member;
use App\Models\MemberBenefitCart;
use App\Models\MemberBenefit;
use App\Helper\Helper;

class FamilyBenefit extends Model
{
    use HasFactory;
    use FamilyBenefitTrait;

    public $timestamps = false;
    protected $guarded = ["id"];
    protected $attributes = [
        'benefit_option_id' => 0,
        'prerequisite_benefit_id' => 0
    ];

    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class, 'member_id', 'id')->select('id', 'name');
    }

    public function benefit(): BelongsTo
    {
        return $this->belongsTo(Benefit::class, 'benefit_id', 'id')->select('id', 'name');
    }

    public function benefits(): BelongsTo
    {
        return $this->belongsTo(BenefitGroup::class, 'benefit_group_id', 'id')->select('id', 'name');
    }

  

    public function getFinalFamilyBenefits($member_id, $dont_omit_prequisites = false, $no_family_option = false)
{
    $memberBenefitsCart = new MemberBenefitCart();
    $memberBenefit = new MemberBenefit();

    $member = Member::find($member_id);

    $selectedFamilyBenefitsData = $memberBenefitsCart->getFamilyBenefits($member_id, $no_family_option);
    $predefinedFamilyBenefits = $memberBenefit->getFamilyBenefits($member_id, true);

    if($no_family_option)
    {
        foreach($predefinedFamilyBenefits as $memberFamilyId => $memberFamily)
        {
            foreach($memberFamily as $benefitId => $familyBenefit)
            {
                if(! isset($selectedFamilyBenefitsData[$memberFamilyId][$benefitId]))
                {
                    $tmp = $predefinedFamilyBenefits[$memberFamilyId][$benefitId];
                    $tmp['real_price'] = $tmp['real_price'] / $member->family_percentage * 100;
                    $selectedFamilyBenefitsData[$memberFamilyId][$benefitId] = $tmp;
                }
            }
        }
    }

    $finalFamilyBenefits = $predefinedFamilyBenefits;
    foreach($selectedFamilyBenefitsData as $key => $selectedBenefit)
    {
        foreach($selectedBenefit as $key_2 => $familyMemberBenefit)
        {
            if($familyMemberBenefit['real_price'] != '0.00')
            {
                $finalFamilyBenefits[$key][$key_2] = $familyMemberBenefit;
            }
            else
            {
                unset($finalFamilyBenefits[$key][$key_2]);
            }
        }
    }

    if($dont_omit_prequisites)
    {
        return $finalFamilyBenefits;
    }

    // Aile hakları içerisinde öngereksinim olarak seçilmiş olan bir hak varsa, onun seçili olmadığı kişilere
    // Bu hakkı tanımıyoruz
    foreach($finalFamilyBenefits as $benefit_id => $familyBenefit)
    {
        foreach($familyBenefit as $member_family_id => $memberFamilyBenefit)
        {
            if($memberFamilyBenefit['prerequisite_benefit_id'])
            {
                if(! $finalFamilyBenefits[$memberFamilyBenefit['prerequisite_benefit_id']][$member_family_id] || $finalFamilyBenefits[$memberFamilyBenefit['prerequisite_benefit_id']][$member_family_id]['real_price'] == '0.00')
                {
                    unset($finalFamilyBenefits[$benefit_id][$member_family_id]);
                }
            }
        }
    }

    return $finalFamilyBenefits;
}


}
