<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;


class MemberFamily extends Model
{
    use HasFactory;
    protected $table = 'member_family';
    public $timestamps = false;
    protected $guarded = ["id"];
    protected $attributes = [
        'update_status' => 1,
        'order_no' => 0
    ];
    

    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class, 'member_id', 'id')->select('id', 'name');
    }

    public function getChildren($memberId)
    {
        return $this->where([
            ['type', '=', 'child'],
            ['member_id', '=', $memberId]
        ])->get();
    }

    public function getPartner($memberId)
    {
        return $this->where([
            ['type', '=', 'partner'],
            ['member_id', '=', $memberId]
        ])->first();
    }

}
