<?php

namespace App\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Auth\Events\Login;
use Illuminate\Support\Facades\Session;
class UpdateLastLoginDate
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Login $event)
    {
       
            $user = $event->user;

            // Guard türü tespiti
            $guard = null;
            if (auth('web')->check()) {
                $guard = 'web';
               
            } elseif (auth('member')->check()) {
                $guard = 'member';
               
            }
         
            Session::put('previous_last_login_date', $user->last_login_date);
            if ($guard == 'web') {
                //Admin panel user'ı
                 $user->last_login_date = now();
                 $user->save();
            } elseif ($guard == 'member') {
                // Normal user
                $user->last_login_date = now();
                $user->save();
            }

    }

    
}
