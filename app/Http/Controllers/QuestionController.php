<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Member;
use App\Models\Question;
use App\Models\QuestionOption;
use Illuminate\Support\Facades\Auth;

class QuestionController extends Controller
{
    public function init() {
        
      
        $member = Member::where('id', Auth::guard('member')->id())->first();

        if ($member) {
            $member_id = $member->id;
           
        }
       
        $questions = (new Question())->getUnansweredQuestions($member_id);
    
       
        $options = (new QuestionOption())->getOptions();
        $qo = [];
        foreach ($options as $o) {
            $qo[$o->question_id][$o->id] = $o->option;
        }
    
       
        return view('survey', [
            'questions' => $questions,
            'options' => $qo,
        ]);
    }

    public function answer(Request $request)
    {
       
        $member = Member::where('id', Auth::guard('member')->id())->first();

        if ($member) {
            $member_id = $member->id;
          
        }else{
            die;
        }

        $question_answers = $request->input('question_answers');


        foreach ($question_answers as $question_id => $answer) {
            if (!empty($answer)) {
                $data = [
                    'member_id' => $member_id,
                    'question_id' => $question_id,
                    'answer' => $answer
                ];

                $m_question=new Question();
                $m_question->insertAnswer($data);
            }
        }

        // Başarılı yanıt döndür
        return response()->json(['message' => 'Cevaplar başarıyla kaydedilmiştir.']);
    }
}
