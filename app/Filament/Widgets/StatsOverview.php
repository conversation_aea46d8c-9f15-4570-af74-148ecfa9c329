<?php

namespace App\Filament\Widgets;

use App\Models\Member;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Card;
use Illuminate\Support\Facades\Session;
use Carbon\Carbon;

class StatsOverview extends BaseWidget
{
    protected function getCards(): array
    {

        $previousLoginDate = Session::get('previous_last_login_date');
        $formattedDate = $previousLoginDate ? Carbon::parse($previousLoginDate)->format('d-m-Y H:i:s') : 'N/A';
               
        return [
            Card::make('Members', Member::count()),
            Card::make('Orders', '0'),
            Card::make('User Previous Login Date', $formattedDate),
        ];
    }
}
