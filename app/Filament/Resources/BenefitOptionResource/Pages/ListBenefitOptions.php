<?php

namespace App\Filament\Resources\BenefitOptionResource\Pages;

use App\Filament\Resources\BenefitOptionResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBenefitOptions extends ListRecords
{
    protected static string $resource = BenefitOptionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
