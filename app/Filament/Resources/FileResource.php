<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FileResource\Pages;
use App\Filament\Resources\FileResource\RelationManagers;
use App\Models\File;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class FileResource extends Resource
{
    protected static ?string $model = File::class;
    protected static ?string $navigationGroup = 'Site Management';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->maxLength(255)
                    ->required()
                    ->reactive(),
                    Forms\Components\TextInput::make('name_en')->label('Name/EN')
                    ->maxLength(255),
                    Forms\Components\FileUpload::make('filename')
                    ->image()
                    ->enableOpen()
                    ->enableDownload()
                    ->acceptedFileTypes(['jpg', 'png', 'gif', 'application/pdf'])
                    // ->getUploadedFileNameForStorageUsing(function (TemporaryUploadedFile $file) use ($form) {
                    //     $nameInput = $form->getState('name') ?? 'default'; // 'name' alanından değeri al
                    //     $slug = Str::slug($nameInput); // Slug oluştur
                    //     return $slug . '.' . $file->getClientOriginalExtension(); // Dosya adını oluştur
                    // })
                    ->getUploadedFileNameForStorageUsing(function (TemporaryUploadedFile $file) {
                        $randomNumber = substr(str_shuffle(str_repeat($x='0123456789', ceil(5/strlen($x)) )),1,5);
                        return 'file_' . $randomNumber . '.' . $file->getClientOriginalExtension(); 
                    })
                    ->directory('pdfs')
                    ->disk('public'),
                    Forms\Components\FileUpload::make('filename_en')->label('File/EN')
                    ->image()
                    ->enableOpen()
                    ->enableDownload()
                    ->acceptedFileTypes(['jpg', 'png', 'gif', 'application/pdf'])
                    ->getUploadedFileNameForStorageUsing(function (TemporaryUploadedFile $file) {
                        $randomNumber = substr(str_shuffle(str_repeat($x='0123456789', ceil(5/strlen($x)) )),1,5);
                        return 'file_' . $randomNumber . '.' . $file->getClientOriginalExtension(); 
                    })
                    ->directory('pdfs') 
                    ->disk('public'),
                    Forms\Components\TextInput::make('link')
                    ->maxLength(255)
                    ->disabled(),
                    Forms\Components\TextInput::make('link_en')
                    ->maxLength(255)
                    ->disabled(),
                    
   
            ]);
    }



    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('name_en')->label('Name/EN')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('link')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('link_en')->label('Link/EN')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFile::route('/'),
            'create' => Pages\CreateFile::route('/create'),
            'edit' => Pages\EditFile::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
