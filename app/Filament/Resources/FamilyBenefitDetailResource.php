<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FamilyBenefitDetailResource\Pages;
use App\Filament\Resources\FamilyBenefitDetailResource\RelationManagers;
use App\Models\FamilyBenefitDetail;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\Select;
use Closure;
use App\Models\FamilyBenefit;
use Illuminate\Support\Facades\DB;

class FamilyBenefitDetailResource extends Resource
{
    protected static ?string $model = FamilyBenefitDetail::class;

    protected static ?string $navigationGroup = 'Members & Benefits';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        $benefits = DB::table('family_benefits')
        ->join('benefit_groups', 'family_benefits.benefit_group_id', '=', 'benefit_groups.id')
        ->select('family_benefits.id', 'benefit_groups.name')
        ->get();

    $benefitsOptions = $benefits->pluck('name', 'id');
        return $form
        ->schema([
            Forms\Components\Select::make('family_benefit_id')
            ->label('Family Benefit')
            ->options($benefitsOptions)
            ->required(),
            Forms\Components\Select::make('relation')->label('Yakınlık Derecesi')
            ->options([
                'cocuk' => 'Çocuk',
                'es' => 'Eş',
            ])
            ->reactive()
            ->afterStateUpdated(function (\Filament\Forms\Set $set, $state) {
                if ($state !== 'cocuk') {
                    $set('age_start', null);
                    $set('age_end', null);
                }
            }),

            Forms\Components\TextInput::make('age_start')
            ->label('Başlangıç Yaşı')
            ->hidden(fn (\Filament\Forms\Get $get) => $get('relation') !== 'cocuk')
            ->reactive(),

            Forms\Components\TextInput::make('age_end')
            ->label('Bitiş Yaşı')
            ->hidden(fn (\Filament\Forms\Get $get) => $get('relation') !== 'cocuk')
            ->reactive(),
            Forms\Components\TextInput::make('price')->label('Brüt Fiyat'),
            Forms\Components\TextInput::make('price_original')->label('Piyasa Fiyatı')
        ]);

    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('benefits.name')->label('Benefit Group Name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('benefit.name')->label('Benefit Name')->sortable()->searchable(),

            ])

            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFamilyBenefitsDetails::route('/'),
            'create' => Pages\CreateFamilyBenefitDetail::route('/create'),
            'edit' => Pages\EditFamilyBenefitDetail::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
