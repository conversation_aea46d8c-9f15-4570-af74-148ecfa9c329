<?php

namespace App\Filament\Resources\BenefitGroupResource\Pages;

use App\Filament\Resources\BenefitGroupResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBenefitGroup extends EditRecord
{
    protected static string $resource = BenefitGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
