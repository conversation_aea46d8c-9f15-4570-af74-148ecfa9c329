<?php

namespace App\Filament\Resources\BenefitGroupResource\Pages;

use App\Filament\Resources\BenefitGroupResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBenefitGroups extends ListRecords
{
    protected static string $resource = BenefitGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
