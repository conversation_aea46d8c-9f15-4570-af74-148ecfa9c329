<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MemberFamilyResource\Pages;
use App\Filament\Resources\MemberFamilyResource\RelationManagers;
use App\Models\MemberFamily;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Filament\Tables\Filters\Layout;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Columns\DateTimeColumn;
use Filament\Forms\Components\DateTimePicker;
use Filament\Tables\Columns\TextColumn;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;

class MemberFamilyResource extends Resource
{
    protected static ?string $model = MemberFamily::class;
    protected static ?string $navigationGroup = 'Members & Benefits';
    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('member_id')
                ->relationship('member', 'name'),
                Forms\Components\TextInput::make('name')
                    ->maxLength(255)
                    ->required(),
                    Forms\Components\TextInput::make('identity_number')
                    ->maxLength(255)
                    ->required(),
                    Forms\Components\Select::make('type')->label('ilişki Türü')
                    ->options([
                        'cocuk' => 'Çocuk',
                        'es' => 'Eş',
                    ]),
                    Forms\Components\DatePicker::make('birth_date'),
            ]);

    }


    public static function table(Table $table): Table
    {

        return $table
            ->columns([
                Tables\Columns\TextColumn::make('firm.name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('department.name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('identity_number')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('is_approved')->sortable(),
                Tables\Columns\TextColumn::make('name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('email')->sortable()->searchable(),
                // Tables\Columns\TextColumn::make('approval_date')->formatStateUsing(function ($value, $entry) {
                //     return $entry->formatted_approval_date;
                // }),
                Tables\Columns\TextColumn::make('approval_date')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('status')->sortable(),

                ])
            ->filters([
                //
            ],

            layout: \Filament\Tables\Enums\FiltersLayout::AboveContent,
            )
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMemberFamily::route('/'),
            'create' => Pages\CreateMemberFamily::route('/create'),
            'edit' => Pages\EditMemberFamily::route('/{record}/edit'),

        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
