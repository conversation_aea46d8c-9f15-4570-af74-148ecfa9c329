<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SliderResource\Pages;
use App\Filament\Resources\SliderResource\RelationManagers;
use App\Models\Slider;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SliderResource extends Resource
{
    protected static ?string $model = Slider::class;
    protected static ?string $navigationGroup = 'Site Management';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('order_no')
                ->numeric()
                ->required()
                ->columnSpan(2),
                Forms\Components\TextInput::make('link')
                    ->maxLength(255),
                    Forms\Components\TextInput::make('link_en')->label('Link/EN')
                    ->maxLength(255),
                Forms\Components\TextInput::make('name')
                    ->maxLength(255),
                    Forms\Components\TextInput::make('name_en')->label('Name/EN')
                    ->maxLength(255),
                    Forms\Components\FileUpload::make('image')
                    ->image()
                    ->enableOpen()
                    ->enableDownload(),
                    Forms\Components\FileUpload::make('image_en')->label('Image/EN')
                    ->image()
                    ->enableOpen()
                    ->enableDownload(),


            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order_no')->sortable(),
                Tables\Columns\TextColumn::make('name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('name_en')->label('Name/EN')->sortable()->searchable(),
                Tables\Columns\ImageColumn::make('image')->sortable()->searchable(),
                Tables\Columns\ImageColumn::make('image_en')->label('Image/EN')->sortable()->searchable()
            ])
            ->defaultSort("order_no")
            ->reorderable("order_no")
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSliders::route('/'),
            'create' => Pages\CreateSlider::route('/create'),
            'edit' => Pages\EditSlider::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
