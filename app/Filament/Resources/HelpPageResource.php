<?php

namespace App\Filament\Resources;

use App\Filament\Resources\HelpPageResource\Pages;
use App\Filament\Resources\HelpPageResource\RelationManagers;
use App\Models\HelpPage;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class HelpPageResource extends Resource
{
    protected static ?string $model = HelpPage::class;
    protected static ?string $navigationGroup = 'Site Management';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('order_no')
                ->numeric()
                ->required()
                ->columnSpan(2),
                Forms\Components\TextInput::make('link')
                    ->maxLength(255),
                    Forms\Components\TextInput::make('link_en')->label('Link/EN')
                    ->maxLength(255),
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                    Forms\Components\TextInput::make('name_en')->label('Name/EN')
                    ->maxLength(255),
                    Forms\Components\FileUpload::make('image')
                    ->image()
                    ->enableOpen()
                    ->enableDownload(),
                    Forms\Components\FileUpload::make('image_en')->label('Image/EN')
                    ->image()
                    ->enableOpen()
                    ->enableDownload(),
                    Forms\Components\RichEditor::make('description')
                    ->maxLength(65535),
                    Forms\Components\RichEditor::make('description_en')->label('Description/EN')
                    ->maxLength(65535),


            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order_no')->sortable(),
                Tables\Columns\TextColumn::make('name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('name_en')->label('Name/EN')->sortable()->searchable(),
                Tables\Columns\ImageColumn::make('image')->sortable()->searchable(),
                Tables\Columns\ImageColumn::make('image_en')->label('Image/EN')->sortable()->searchable()
            ])
            ->defaultSort("order_no")
            ->reorderable("order_no")
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListHelpPages::route('/'),
            'create' => Pages\CreateHelpPage::route('/create'),
            'edit' => Pages\EditHelpPage::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
