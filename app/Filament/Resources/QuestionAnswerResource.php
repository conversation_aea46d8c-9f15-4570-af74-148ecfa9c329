<?php

namespace App\Filament\Resources;

use App\Filament\Resources\QuestionAnswerResource\Pages;
use App\Filament\Resources\QuestionResource\RelationManagers;
use App\Models\QuestionAnswer;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class QuestionAnswerResource extends Resource
{
    protected static ?string $model = QuestionAnswer::class;

    protected static ?string $navigationGroup = 'Questions & Surveys';

    protected static ?string $navigationIcon = 'heroicon-o-question-mark-circle';

    public static function form(Form $form): Form
    {
        return $form
    ->schema([
        Forms\Components\Select::make('question_id')
            ->relationship('question', 'text', function ($query) {
                return $query->select('id', 'text')->whereNotNull('text');
            })
            ->columnSpan(12),
        Forms\Components\Select::make('question_id')
            ->relationship('question', 'text_en', function ($query) {
                return $query->select('id', 'text_en')->whereNotNull('text_en');
            })
            ->columnSpan(12),
        Forms\Components\Select::make('member_id')
            ->relationship('member', 'name', function ($query) {
                return $query->select('id', 'name')->whereNotNull('name');
            })
            ->columnSpan(12),
        Forms\Components\RichEditor::make('answer')
            ->columnSpan(12),
        Forms\Components\DateTimePicker::make('date')
    ]);

    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('date')
                ->sortable(),
                Tables\Columns\TextColumn::make('member.identity_number')->label('Identity Number')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('member.name')->label('Member Name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('question.text')->label('Question')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('question.text_en')->label('Question/EN')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('answer')->sortable()->searchable(),
                // Tables\Columns\TextColumn::make('answer_en')->label('Answer/EN')->sortable()->searchable()
            ])
            ->defaultSort("date")
            ->reorderable("date")
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    
    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListQuestions::route('/'),
            'create' => Pages\CreateQuestion::route('/create'),
            'edit' => Pages\EditQuestion::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
