<?php

namespace App\Filament\Resources\FamilyBenefitResource\Pages;

use App\Filament\Resources\FamilyBenefitResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\EditRecord;

class EditFamilyBenefit extends EditRecord
{
    protected static string $resource = FamilyBenefitResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
