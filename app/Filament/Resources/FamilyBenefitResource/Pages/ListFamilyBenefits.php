<?php

namespace App\Filament\Resources\FamilyBenefitResource\Pages;

use App\Filament\Resources\FamilyBenefitResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFamilyBenefits extends ListRecords
{
    protected static string $resource = FamilyBenefitResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
