<?php

namespace App\Filament\Resources;

use App\Filament\Resources\QuestionOptionResource\Pages;
use App\Filament\Resources\QuestionResource\RelationManagers;
use App\Models\QuestionOption;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class QuestionOptionResource extends Resource
{
    protected static ?string $model = QuestionOption::class;

    protected static ?string $navigationGroup = 'Questions & Surveys';

    protected static ?string $navigationIcon = 'heroicon-o-question-mark-circle';

    public static function form(Form $form): Form
    {
        // return $form
        //     ->schema([
        //         Forms\Components\TextInput::make('order_no')
        //         ->numeric()
        //         ->required(),
        //         Forms\Components\Select::make('question_id')
        //             ->relationship('question', 'text'),
        //         Forms\Components\TextInput::make('option')
        //             ->maxLength(255),

        //     ]);

        return $form
        ->schema([
            Forms\Components\TextInput::make('order_no')
                ->numeric()
                ->required(),
            Forms\Components\Select::make('question_id')
                ->relationship('question', 'text', function ($query) {
                    return $query->select('id', 'text')->whereNotNull('text');
                }),
            Forms\Components\TextInput::make('option')
                ->maxLength(255),
            Forms\Components\Select::make('question_id')
                ->label('Bir Soru Seçin')
                ->relationship('question', 'text_en', function ($query) {
                    return $query->select('id', 'text_en')->whereNotNull('text_en');
                })
                ->extraAttributes(['style' => 'display:none']),
            Forms\Components\TextInput::make('option_en')
                ->label('Option/EN')
                ->maxLength(255),
        ]);
    
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order_no')
                ->sortable(),
                Tables\Columns\TextColumn::make('option')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('option_en')->label('Option/EN')->sortable()->searchable(),

            ])
            ->defaultSort("order_no")
            ->reorderable("order_no")
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }


    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListQuestions::route('/'),
            'create' => Pages\CreateQuestion::route('/create'),
            'edit' => Pages\EditQuestion::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
