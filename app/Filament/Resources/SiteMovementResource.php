<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SiteMovementResource\Pages;
use App\Filament\Resources\SiteMovementResource\RelationManagers;
use App\Models\SiteMovement;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SiteMovementResource extends Resource
{
    protected static ?string $model = SiteMovement::class;
    protected static ?string $navigationGroup = 'Site Management';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?int $navigationSort = -2;

    // public static function form(Form $form): Form
    // {
    //     return $form
    //         ->schema([
    //             //
    //         ]);
    // }

    public static function form(Form $form): Form
    {
        $siteMovement = $form->model;


        if ($siteMovement && $siteMovement->id) {

            if ($siteMovement->id == 1) {
                $schema = [
                    Forms\Components\TextInput::make('name')
                        ->columnSpan(2)
                        ->disabled(),
                    Forms\Components\Toggle::make('extra1')->label('Site Kapat')
                        ->columnSpan(2),
                    Forms\Components\FileUpload::make('image')
                        ->image()
                        ->enableOpen()
                        ->enableDownload()
                        ->label('Site Kapama Görseli'),
                ];
            } elseif ($siteMovement->id == 2) {

                $schema = [
                    Forms\Components\TextInput::make('name')
                        ->columnSpan(2)
                        ->disabled(),
                    Forms\Components\TextInput::make('extra1')->label('Mail')
                        ->columnSpan(2),
                    Forms\Components\TextInput::make('extra2')->label('Gönderen Adı')
                        ->columnSpan(2),
                ];
            } elseif ($siteMovement->id == 3) {
                $schema = [
                    Forms\Components\TextInput::make('name')
                        ->columnSpan(2)
                        ->disabled(),
                    Forms\Components\TextInput::make('extra1')->label('Mail'),
                    Forms\Components\TextInput::make('extra2')->label('Gönderen Adı'),
//                    Forms\Components\Select::make('extra4')->label('Mail Dili')
//                        ->required()
//                        ->options([
//                            '1' => 'Türkçe',
//                            '2' => 'İngilizce'
//                        ])
                ];
            }
        }

        return $form->schema($schema);
    }


    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSiteMovements::route('/'),
            'edit' => Pages\EditSiteMovement::route('/{record}/edit'),
            'import' => Pages\ImportMailings::route('/import'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

//    public static function canViewAny(): bool
//    {
//        return static::can('viewAny');
//    }
}
