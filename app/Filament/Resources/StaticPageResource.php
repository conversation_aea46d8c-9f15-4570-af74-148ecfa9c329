<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StaticPageResource\Pages;
use App\Filament\Resources\StaticPageResource\RelationManagers;
use App\Models\StaticPage;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;

class StaticPageResource extends Resource
{
    protected static ?string $model = StaticPage::class;
    protected static ?string $navigationGroup = 'Site Management';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->maxLength(255)
                    ->required(),
                    Forms\Components\TextInput::make('name_en')->label('Name/EN')
                    ->maxLength(255),
                    Forms\Components\TextInput::make('title')
                    ->maxLength(255),
                    Forms\Components\TextInput::make('title_en')->label('Title/EN')
                    ->maxLength(255),
                    Forms\Components\RichEditor::make('content'),
                    Forms\Components\RichEditor::make('content_en')->label('Content/EN'),
                    Forms\Components\Hidden::make('seo'),
                    Forms\Components\Toggle::make('before_login'),
                    Forms\Components\Toggle::make('is_visible'),
   
            ]);
    }

    public function saving()
    {
        // Automatically generate and set the SEO value if it's empty
        if (empty($this->seo)) {
            $this->seo = Str::slug($this->title);
        }
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('name_en')->label('Name/EN')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('title')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('title_en')->label('Title/EN')->sortable()->searchable(),

            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                // Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStaticPage::route('/'),
            'create' => Pages\CreateStaticPage::route('/create'),
            'edit' => Pages\EditStaticPage::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
