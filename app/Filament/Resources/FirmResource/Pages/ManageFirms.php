<?php

namespace App\Filament\Resources\FirmResource\Pages;

use App\Filament\Resources\FirmResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageFirms extends ManageRecords
{
    protected static string $resource = FirmResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
