<?php

namespace App\Filament\Resources\MemberFamilyResource\Pages;

use App\Filament\Resources\MemberFamilyResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMemberFamily extends ListRecords
{
    protected static string $resource = MemberFamilyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
