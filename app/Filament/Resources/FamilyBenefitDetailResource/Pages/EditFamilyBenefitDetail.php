<?php

namespace App\Filament\Resources\FamilyBenefitDetailResource\Pages;

use App\Filament\Resources\FamilyBenefitDetailResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\EditRecord;

class EditFamilyBenefitDetail extends EditRecord
{
    protected static string $resource = FamilyBenefitDetailResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
