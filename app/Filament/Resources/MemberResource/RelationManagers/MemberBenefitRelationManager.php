<?php

namespace App\Filament\Resources\MemberResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Get;
use Illuminate\Support\Collection;

class MemberBenefitRelationManager extends RelationManager
{
    protected static string $relationship = 'memberBenefit';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('benefit_id')
                ->label('Benefit')
                ->relationship('benefit', 'name')
                ->live(),
            Forms\Components\Select::make('benefit_option_id')
                ->label('Benefit Option')
                ->options(fn (Get $get): Collection => \App\Models\BenefitOption::query()
                    ->when($get('benefit_id'), function ($query, $benefitId) {
                        return $query->where('benefit_id', $benefitId);
                    })
                    ->pluck('name', 'id')),
            Forms\Components\TextInput::make('amount')
                ->maxLength(255),
            Forms\Components\Toggle::make('update_status')
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('member_id')
            ->columns([
                Tables\Columns\TextColumn::make('benefit.name')->label('Benefit Name'),
                Tables\Columns\TextColumn::make('benefitOption.name')->label('Benefit Option Name'),
                Tables\Columns\TextColumn::make('amount')->label('amount'),
                
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }
}
