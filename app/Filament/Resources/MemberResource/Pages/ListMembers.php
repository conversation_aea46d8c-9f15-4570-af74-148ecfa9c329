<?php

namespace App\Filament\Resources\MemberResource\Pages;

use App\Filament\Resources\MemberResource;
use Filament\Pages\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\ListRecords;

class ListMembers extends ListRecords
{
    protected static string $resource = MemberResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Action::make("Member Export-Import")
            ->button()
            ->color("success")
            ->url("members/import"),
            Action::make('Benefit Export-Import')
            ->button()
            ->color("success")
            ->url('members/excel-actions-benefit')
        ];
    }
}
