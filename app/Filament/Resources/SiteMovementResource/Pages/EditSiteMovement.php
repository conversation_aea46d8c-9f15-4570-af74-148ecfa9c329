<?php

namespace App\Filament\Resources\SiteMovementResource\Pages;

use App\Filament\Resources\SiteMovementResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use App\Http\Controllers\SiteMovementController;
use Illuminate\Database\Eloquent\Model;

class EditSiteMovement extends EditRecord
{
    protected static string $resource = SiteMovementResource::class;


    // protected function handleRecordUpdate(Model $record, array $data): Model
    // {
    //     $updatedRecord = parent::handleRecordUpdate($record, $data);

    //     if (isset($data['extra3']) && $data['extra3'] == 1) {
    //         $controller = new SiteMovementController();
    //         $controller->sendCustomEmails();
    //     }

    //     return $updatedRecord;
    // }
}
