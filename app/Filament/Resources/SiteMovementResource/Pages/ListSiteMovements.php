<?php

namespace App\Filament\Resources\SiteMovementResource\Pages;

use App\Filament\Resources\SiteMovementResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

use Filament\Actions\Action;
class ListSiteMovements extends ListRecords
{
    protected static string $resource = SiteMovementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make("Toplu Mail Gönderimi")
            ->button()
            ->color("success")
            ->url("site-movements/import"),
           
        ];
    }
}
