<?php

namespace App\Filament\Resources\MemberBenefitPrefResource\Pages;

use App\Filament\Resources\MemberBenefitPrefResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMemberBenefitPrefs extends ListRecords
{
    protected static string $resource = MemberBenefitPrefResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
