<?php

namespace App\Filament\Resources\MemberBenefitPrefResource\Pages;

use App\Filament\Resources\MemberBenefitPrefResource;
use Filament\Resources\Pages\Page;
use App\Models\Member; 

class SortBenefitMember extends Page
{
    public $members;

    protected static string $resource = MemberBenefitPrefResource::class;

    protected static string $view = 'filament.resources.member-benefit-pref-resource.pages.sort-benefit-member';

    public function mount(): void
    {
        $this->members = Member::select('id', 'name', 'email')->get();
    }

    public function getMemberBenefits($id)
    {
        $member = Member::find($id);

        if ($member) {
            $this->members = $member->memberBenefits;
        } else {
            $this->members = [];
        }

        return $this->members;
    }
}
