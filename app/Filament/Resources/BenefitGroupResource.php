<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BenefitGroupResource\Pages;
use App\Filament\Resources\BenefitGroupResource\RelationManagers;
use App\Models\BenefitGroup;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class BenefitGroupResource extends Resource
{
    protected static ?string $model = BenefitGroup::class;
    protected static ?string $navigationGroup = 'Members & Benefits';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('order_no')
                    ->required()
                    ->numeric()
                    ->columnSpan(2),
                Forms\Components\TextInput::make('name')
                    ->maxLength(255)
                ->required(),
                Forms\Components\TextInput::make('name_en')->label('Name/EN')
                    ->maxLength(255),
                Forms\Components\RichEditor::make('description')
                    ->maxLength(65535),
                    Forms\Components\RichEditor::make('description_en')->label('Description/EN')
                    ->maxLength(65535),
                    Forms\Components\Toggle::make('status')
                    ->columnSpan(2),
                Forms\Components\FileUpload::make('image')
                    ->image()
                    ->enableOpen()
                    ->enableDownload(),
                    Forms\Components\FileUpload::make('image_en')->label('Image/EN')
                    ->image()
                    ->enableOpen()
                    ->enableDownload(),
                    Forms\Components\TextInput::make('announcement_id'),
                    Forms\Components\Toggle::make('can_be_negative'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order_no')->sortable(),
                Tables\Columns\TextColumn::make('name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('name_en')->label('Name/EN')->sortable()->searchable(),
                Tables\Columns\ImageColumn::make("image")->sortable()->searchable(),
                Tables\Columns\ImageColumn::make("image_en")->label('Image/EN')->sortable()->searchable(),
            ])
            ->defaultSort("order_no")
            ->reorderable("order_no")
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBenefitGroups::route('/'),
            'create' => Pages\CreateBenefitGroup::route('/create'),
            'edit' => Pages\EditBenefitGroup::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
