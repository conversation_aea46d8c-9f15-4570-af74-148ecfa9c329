<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MemberBenefitPrefResource\Pages;
use App\Filament\Resources\MemberBenefitPrefResource\RelationManagers;
use App\Models\MemberBenefitPref;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\MemberBenefitPrefResource\Pages\SortBenefitMember;
use Filament\Resources\Pages\EditRecord;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Columns\Text;
use Filament\Forms\Components\Select;
use Filament\Forms\Get;
use Illuminate\Support\Collection;

class MemberBenefitPrefResource extends Resource
{
    protected static ?string $model = MemberBenefitPref::class;

    protected static ?string $navigationGroup = 'Members & Benefits';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
{
    return $form
        ->schema([
            Forms\Components\Select::make('member_id')
                ->relationship('member', 'name'),
            Forms\Components\Select::make('benefit_id')
                ->label('Benefit')
                ->relationship('benefit', 'name')
                ->live(),
            Forms\Components\Select::make('benefit_option_id')
                ->label('Benefit Option')
                ->options(function (Get $get) {
                    $options = \App\Models\BenefitOption::query()
                        ->when($get('benefit_id'), function ($query, $benefitId) {
                            return $query->where('benefit_id', $benefitId);
                        })
                        ->pluck('name', 'id');
                    if ($options->isEmpty()) {
                        return collect(['0' => 'Varsayılan']);
                    }
                    return $options;
                })
                ->default('0'),
            Forms\Components\TextInput::make('price')
                ->maxLength(255),
            Forms\Components\Toggle::make('is_visible'),
        ]);
}

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('member.name')->label('Member Name')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('price')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('benefit.name')->label('Benefit Name')->searchable()->sortable(),

            ])

            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMemberBenefitPrefs::route('/'),
            'create' => Pages\CreateMemberBenefitPref::route('/create'),
            'edit' => Pages\EditMemberBenefitPref::route('/{record}/edit'),
            'sort' => Pages\SortBenefitMember::route('/sort'),
            'import-export-operations' => Pages\ImportExportOperations::route('/import-export-operations'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    public static function actions()
    {
        return [
            Components\Button::make('Get Benefits')
                ->primary()
                ->redirect(function ($record) {
                    return route('get-member-benefits', $record);
                }),
        ];
    }
}
