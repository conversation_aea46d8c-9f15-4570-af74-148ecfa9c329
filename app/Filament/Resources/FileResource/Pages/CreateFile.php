<?php

namespace App\Filament\Resources\FileResource\Pages;

use App\Filament\Resources\FileResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Pages\Actions\CreateAction;

class CreateFile extends CreateRecord
{
    protected static string $resource = FileResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        if (isset($data['filename'])) {
            $data['link'] = env('APP_FILE_LINK') . "storage/" . $data['filename'];
        }

        if (isset($data['filename_en'])) {
            $data['link_en'] = env('APP_FILE_LINK') . "storage/" . $data['filename_en'];
        }

        return $data;
    }

}
