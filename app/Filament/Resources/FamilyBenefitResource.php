<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FamilyBenefitResource\Pages;
use App\Filament\Resources\FamilyBenefitResource\RelationManagers;
use App\Models\FamilyBenefit;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FamilyBenefitResource extends Resource
{
    protected static ?string $model = FamilyBenefit::class;

    protected static ?string $navigationGroup = 'Members & Benefits';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('benefit_id')
                    ->relationship('benefit', 'name'),
                    Forms\Components\Select::make('benefit_group_id')
                    ->relationship('benefits', 'name')->label('Benefit Group'),
                    Forms\Components\Toggle::make('has_company_support'),
            ]);
         
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('benefits.name')->label('Benefit Group Name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('benefit.name')->label('Benefit Name')->sortable()->searchable(),

            ])
            
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFamilyBenefits::route('/'),
            'create' => Pages\CreateFamilyBenefit::route('/create'),
            'edit' => Pages\EditFamilyBenefit::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
