<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Filament\Resources\UserResource\RelationManagers;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Support\Facades\Hash;

class UserResource extends Resource
{
    protected static ?string $model = User::class;
    protected static ?string $navigationGroup = 'Users';
    protected static ?string $navigationIcon = 'heroicon-o-user';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->maxLength(255)
                    ->required(),
                    Forms\Components\TextInput::make('password')
                    ->password()
                    ->dehydrateStateUsing(fn ($state, $record) => filled($state) ? Hash::make($state) : $record?->password)
                    ->dehydrated(fn ($state, $record) => is_null($record) || (filled($state) && $state !== $record->getRawOriginal('password')))
                    ->required(fn (string $context): bool => $context === 'create')
                    ->maxLength(255),
                Forms\Components\TextInput::make('email')
                    ->email()
                    ->maxLength(255)
                    ->required(),
            ]);
    }


    // public static function table(Table $table): Table
    // {

    //     return $table
    //         ->columns([
    //             Tables\Columns\TextColumn::make('identity_number')->sortable()->searchable(),
    //             Tables\Columns\TextColumn::make('name')->sortable()->searchable(),
    //             Tables\Columns\TextColumn::make('email')->sortable()->searchable()
    //             ->label('Benefits Approved')
    //             ->sortable()
    //             ->searchable()
    //             ->getStateUsing(function ($record) {
    //                 return $record->is_approved === null ? '0' : $record->is_approved;
    //             }),
    //             ])
    //         ->filters([
    //             //
    //         ],

    //         layout: \Filament\Tables\Enums\FiltersLayout::AboveContent,
    //         )
    //         ->actions([
    //             Tables\Actions\EditAction::make(),
    //             Tables\Actions\DeleteAction::make(),
    //         ])
    //         ->bulkActions([
    //             Tables\Actions\DeleteBulkAction::make(),
    //         ]);
    // }

    public static function table(Table $table): Table
{
    return $table
        ->columns([
            Tables\Columns\TextColumn::make('name')->sortable()->searchable(),
            Tables\Columns\TextColumn::make('email')->sortable()->searchable(),
            
        ])
        ->filters([
            //
        ],
        layout: \Filament\Tables\Enums\FiltersLayout::AboveContent,
        )
        ->actions([
            Tables\Actions\EditAction::make(),
            Tables\Actions\DeleteAction::make(),
        ])
        ->bulkActions([
            Tables\Actions\DeleteBulkAction::make(),
        ]);
}


   

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
           

        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
