{"version": 3, "file": "datepicker.turbo.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;;;;;;;;;;;;ACVwD;AACU;AAC/B;AAEnC,IAAMG,oBAAoB,GAAG,SAAvBA,oBAAoB,CAAIC,YAAY,EAAK;EAC3C,IAAMC,OAAO,GAAGD,YAAY,CAACE,YAAY,CAAC,oBAAoB,CAAC;EAC/D,IAAMC,QAAQ,GAAGH,YAAY,CAACE,YAAY,CAAC,qBAAqB,CAAC;EACjE,IAAME,MAAM,GAAGJ,YAAY,CAACE,YAAY,CAAC,mBAAmB,CAAC;EAC7D,IAAMG,WAAW,GAAGL,YAAY,CAACE,YAAY,CAAC,wBAAwB,CAAC;EACvE,IAAMI,KAAK,GAAGN,YAAY,CAACE,YAAY,CAAC,kBAAkB,CAAC;EAE3D,IAAMK,OAAO,GAAG,CAAC,CAAC;EAClB,IAAIN,OAAO,EAAE;IACTM,OAAO,CAACC,QAAQ,GAAG,IAAI;IACvBD,OAAO,CAACE,QAAQ,GAAG,IAAI;EAC3B;EACA,IAAIN,QAAQ,EAAE;IACVI,OAAO,CAACJ,QAAQ,GAAG,IAAI;EAC3B;EACA,IAAIC,MAAM,EAAE;IACRG,OAAO,CAACH,MAAM,GAAGJ,YAAY,CAACU,YAAY,CAAC,mBAAmB,CAAC;EACnE;EACA,IAAIL,WAAW,EAAE;IACbE,OAAO,CAACF,WAAW,GAAGL,YAAY,CAACU,YAAY,CAC3C,wBAAwB,CAC3B;EACL;EACA,IAAIJ,KAAK,EAAE;IACPC,OAAO,CAACD,KAAK,GAAGN,YAAY,CAACU,YAAY,CAAC,kBAAkB,CAAC;EACjE;EAEA,OAAOH,OAAO;AAClB,CAAC;AAEM,SAASI,eAAe,GAAG;EAC9BC,QAAQ,CAACC,gBAAgB,CAAC,cAAc,CAAC,CAACC,OAAO,CAAC,UAAUd,YAAY,EAAE;IACtE,IAAIJ,+EAAU,CAACI,YAAY,EAAED,oBAAoB,CAACC,YAAY,CAAC,CAAC;EACpE,CAAC,CAAC;EAEFY,QAAQ,CACHC,gBAAgB,CAAC,qBAAqB,CAAC,CACvCC,OAAO,CAAC,UAAUd,YAAY,EAAE;IAC7B,IAAIJ,+EAAU,CAACI,YAAY,EAAED,oBAAoB,CAACC,YAAY,CAAC,CAAC;EACpE,CAAC,CAAC;EAENY,QAAQ,CACHC,gBAAgB,CAAC,oBAAoB,CAAC,CACtCC,OAAO,CAAC,UAAUd,YAAY,EAAE;IAC7B,IAAIH,oFAAe,CACfG,YAAY,EACZD,oBAAoB,CAACC,YAAY,CAAC,CACrC;EACL,CAAC,CAAC;AACV;AAEA,IAAMe,MAAM,GAAG,IAAIjB,mDAAM,CAAC,kBAAkB,EAAE,CAACa,eAAe,CAAC,CAAC;AAChEI,MAAM,CAACC,IAAI,EAAE;;;;;;;;;;;;;ACxDyD;AACtB;AACP;;AAEzC;AACA;AACA,kCAAkC;;AAElC;AACA;AACA,mCAAmC;;AAEnC;AACA;;AAEA;AACA,EAAE,0EAAiB;AACnB;AACA;AACA,MAAM,+DAAU;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACe;AACf;AACA;AACA,cAAc,SAAS;AACvB,cAAc,QAAQ;AACtB;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,0BAA0B,uBAAuB;AACjD,MAAM;AACN,0BAA0B,uBAAuB;AACjD;AACA;;AAEA;AACA,YAAY,OAAO;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,cAAc,iBAAiB;AAC/B;AACA;AACA;AACA;AACA,IAAI,4EAAmB;AACvB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,QAAQ;AACtB,cAAc,OAAO;AACrB;AACA;AACA;AACA,gBAAgB,yEAAU;AAC1B;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE;AAClE;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,2BAA2B;AACxC,SAAS,aAAa;AACtB,aAAa,2BAA2B;AACxC,SAAS,aAAa;AACtB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B,uBAAuB;AACjD,MAAM;AACN,0BAA0B,uBAAuB;AACjD;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;ACjNA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,2DAAe,cAAc,EAAC;;;ACrC9B;;AAEO;AACP;AACA;;AAEA;AACO;AACP;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA,KAAK;AACL;AACA;;;ACrDwD;AACf;AACuB;AACxB;AACS;;AAEjD;AACA;AACA;AACA;AACA,EAAE,EAAE,sBAAc;;AAElB;AACA;AACA;AACA,MAAM,4BAAU;AAChB;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,eAAe,iCAAS;AACxB;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACe;AACf,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,8BAA8B,4CAAmB;AACjD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,8BAAS;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,mBAAmB,iCAAS;AAC5B,kCAAkC,4BAAU;AAC5C,KAAK;AACL;AACA;AACA;AACA,qBAAqB,iCAAS;AAC9B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,sBAAsB,SAAS;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,SAAS;AAC/B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,qCAAqC,6BAAW,CAAC,sBAAc;AAC/D;AACA;AACA,GAAG;;AAEH;AACA;;;ACtQwD;;AAExD,uBAAuB,sCAAoB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,6DAAe,cAAc,EAAC;;;ACtB2C;;AAEzE,qBAAqB,sCAAoB;AACzC,oDAAoD,iCAAe,aAAa,uHAAuH,EAAE;AACzM,uDAAuD,iCAAe,eAAe,sLAAsL,EAAE;AAC7Q;;AAEA,2DAAe,YAAY,EAAC;;;ACP6C;;AAEzE,8BAA8B,sCAAoB;AAClD;AACA,uBAAuB,iCAAe,aAAa,wHAAwH,EAAE;AAC7K;;AAEA,oEAAe,qBAAqB,EAAC;;;ACPS;AACgB;;AAE9D;AACe;AACf;AACA;AACA;AACA,eAAe,SAAS;AACxB;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA,kBAAkB;AAClB;;AAEA;AACA;AACA;AACA,QAAQ,4BAAU;AAClB;AACA;AACA;AACA;AACA;AACA,UAAU,4BAAU;AACpB;AACA;AACA;AACA,QAAQ,iBAAiB;AACzB;AACA;AACA;AACA;;;ACtD2D;AACoC;AAC3C;AACiB;AACb;AACkB;AAC7C;;AAEd,uBAAuB,IAAI;AAC1C;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA,oBAAoB,SAAS,CAAC,sBAAY;AAC1C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,QAAQ,6BAAW;AACnB;AACA;AACA,QAAQ,6BAAW;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,0BAA0B,SAAS,CAAC,+BAAqB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,WAAW;AACnB;AACA,UAAU,WAAW;AACrB;AACA,QAAQ;AACR,QAAQ,WAAW;AACnB;AACA,UAAU,WAAW;AACrB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,yBAAyB,8BAAS;AAClC,kBAAkB,mCAAc;;AAEhC;AACA,gBAAgB,8BAAS;AACzB;AACA;AACA;;AAEA;AACA;AACA,WAAW,oBAAoB;AAC/B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,uCAAuC,0BAAK;AAC5C;AACA;AACA;;AAEA,wBAAwB,kCAAU;AAClC;AACA;AACA;;AAEA;AACA;AACA,0BAA0B,mCAAc;AACxC;AACA,yBAAyB,4BAAO,CAAC,6BAAQ;AACzC,OAAO;AACP;AACA;AACA;AACA,sBAAsB,4BAAO;AAC7B;AACA;;AAEA,2MAA2M,eAAe;AAC1N;AACA;;AAEA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,4BAAU;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;;ACvP4E;AAChC;AACD;AACd;;AAE7B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEe,yBAAyB,IAAI;AAC5C;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA,4BAA4B,SAAS,CAAC,iCAAe,cAAc,uBAAuB;AAC1F;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,QAAQ,6BAAW;AACnB;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,6BAAW;AACnB;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,uBAAuB,8BAAS;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,WAAW,oBAAoB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR,QAAQ,4BAAU;AAClB;AACA;AACA,KAAK,IAAI;AACT;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,mBAAmB,8BAAS;;AAE5B,2MAA2M,eAAe;AAC1N;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;ACrM4E;AACb;AACpB;AACd;;AAE7B;AACA;AACA;;AAEA;AACe,wBAAwB,IAAI;AAC3C;AACA;AACA;;AAEA;AACA;AACA;AACA,2CAA2C,4BAA4B;AACvE;AACA;AACA,4BAA4B,SAAS,CAAC,iCAAe;AACrD;AACA;AACA;;AAEA;AACA,QAAQ,6BAAW;AACnB;AACA;AACA,QAAQ;AACR,uBAAuB,sCAAiB;AACxC,uBAAuB,8BAAS;AAChC;AACA;AACA,QAAQ,6BAAW;AACnB;AACA;AACA,QAAQ;AACR,uBAAuB,sCAAiB;AACxC,uBAAuB,8BAAS;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,kBAAkB,sCAAiB;AACnC;;AAEA;AACA;AACA;AACA,mBAAmB,sCAAiB;AACpC;;AAEA;AACA;AACA,WAAW,oBAAoB;AAC/B;AACA,aAAa,4BAAU,QAAQ,sCAAiB;AAChD,KAAK;AACL;AACA;AACA;AACA,iBAAiB,sCAAiB;AAClC;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,sCAAsC,WAAW,GAAG,UAAU;AAC9D;AACA;;AAEA;AACA;AACA;AACA,mBAAmB,8BAAS;;AAE5B,2MAA2M,eAAe;AAC1N;AACA;AACA;AACA;;AAEA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;;ACzK6C;AACM;;AAE5C;AACP;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D,OAAO;AACjE;;AAEA;AACO;AACP,SAAS,kBAAkB;AAC3B,SAAS,uBAAuB;AAChC;AACA;AACA;AACA,oBAAoB,8BAAS;AAC7B;AACA;AACA,oBAAoB,6BAAQ;AAC5B;AACA;AACA,oBAAoB,6BAAQ;AAC5B;AACA,gBAAgB,8BAAY;AAC5B;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA,uBAAuB,eAAe;AACtC,IAAI;AACJ;AACA;AACA;AACA;;;AC/C0D;AACH;AACG;;AAE1D;AACA;AACA;AACA;AACA;AACA,MAAM,8BAAS;AACf,MAAM,6BAAQ;;AAEd;AACA;;AAEO;AACP;AACA,sBAAsB,0BAAK;AAC3B;AACA;AACA;AACA;AACA;AACA,qCAAqC,cAAc;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP,sBAAsB,YAAY;AAClC;;AAEO;AACP,EAAE,UAAU;AACZ;;AAEO;AACP,EAAE,cAAc;AAChB;;AAEO;AACP,EAAE,cAAc;AAChB;;AAEA;AACO;AACP,iBAAiB,4CAAsB;AACvC;AACA;AACA;;AAEA,SAAS,eAAe;AACxB;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;;;ACrEiF;AAC5C;AAC8C;AACjC;AACS;AAChB;AACI;AACF;AACiB;AASxB;;AAEtC;AACA;AACA;AACA;AACA,MAAM,WAAW;AACjB,MAAM;AACN;AACA,MAAM,WAAW;AACjB;AACA;AACA;AACA;AACA,IAAI,eAAe;AACnB;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,IAAI,eAAe;AACnB;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,WAAW;AACjB,MAAM;AACN,MAAM,WAAW;AACjB;AACA;AACA,MAAM,6BAAW,wBAAwB,6BAAW;AACpD,WAAW,kBAAkB;AAC7B,yCAAyC,2BAAS,CAAC,0BAAK;AACxD;AACA;AACA;AACA,MAAM,WAAW;AACjB,MAAM;AACN,MAAM,WAAW;AACjB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS,eAAe;AACxB,sCAAsC,4BAAU;AAChD,SAAS,8BAAY;AACrB;;AAEA;AACA;AACA;AACA;AACA,SAAS,uBAAuB;AAChC;;AAEA;AACA;AACA,IAAI,sBAAsB;AAC1B;AACA;AACA,IAAI,sBAAsB;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACe;AACf;AACA;;AAEA,qBAAqB,gCAAsB;AAC3C,mCAAmC,SAAS;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,wCAAwC,aAAa;AACrD;;AAEA;AACA;;AAEA;AACA,IAAI,uCAAiB;AACrB,yBAAyB,kBAAkB,qBAAqB,cAAc;AAC9E,sBAAsB,gBAAgB;AACtC,qCAAqC,sBAAsB;AAC3D,kCAAkC,mBAAmB;AACrD,kCAAkC,mBAAmB;AACrD,mCAAmC,oBAAoB;AACvD,mCAAmC,oBAAoB;AACvD;;AAEA;AACA;AACA,UAAU,QAAQ;AAClB,UAAU,UAAU;AACpB,UAAU,SAAS,QAAQ,iDAAiD;AAC5E,UAAU,SAAS,QAAQ,sDAAsD;AACjF;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI,sBAAsB;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,sBAAsB;AAC1B;;AAEA;AACA,WAAW,kBAAkB;AAC7B,WAAW,oBAAoB;AAC/B;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,SAAS,wBAAwB;AACjC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,QAAQ,wBAAwB,QAAQ;;AAE/E,yBAAyB,IAAI;AAC7B,2BAA2B,KAAK;AAChC;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,sBAAsB;AAC5B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AC7U0C;AACqC;AACZ;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,OAAO,2BAAS;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,4BAAO;AAC1B,QAAQ;AACR,mBAAmB,6BAAQ;AAC3B,QAAQ;AACR,mBAAmB,4BAAO;AAC1B;AACA,cAAc,wBAAO;AACrB;AACA;AACA;AACA,iBAAiB,8BAAS;AAC1B,cAAc,0BAAS;AACvB;AACA;AACA,eAAe,gBAAgB;AAC/B;AACA;AACA;AACA;AACA,iBAAiB,6BAAQ;AACzB,cAAc,yBAAQ;AACtB,qDAAqD,sCAAiB;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA,IAAI,OAAO;AACX;AACA;;AAEA;AACA,SAAS,eAAe;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,iCAAiC,mDAAmD;AACpF;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,cAAc;AACxB,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA,UAAU,cAAc;AACxB,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA,UAAU,UAAU;AACpB,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;;AAEA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;;;AC7MuD;AAChB;;AAEvC;AACO;AACP;AACA;AACA;AACA;AACA;AACA,MAAM,4CAAsB;AAC5B;AACA;AACA,EAAE,OAAO;AACT;;;ACdoE;AAChC;AACuB;AACW;AACvB;AACU;AACA;AACjB;AACqB;AAC0C;AAC7C;;AAE1D;AACA;AACA,eAAe,kCAAU;AACzB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS,uCAAuC;AAChD;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,iCAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,2BAAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS,4BAA4B;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,OAAO,yBAAyB;AAChC;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,sBAAsB;AAC1B,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACe;AACf;AACA;AACA,cAAc,SAAS;AACvB,cAAc,QAAQ;AACtB,cAAc,iBAAiB;AAC/B;AACA;AACA;AACA,mCAAmC;AACnC;AACA;;AAEA;AACA;AACA;AACA;AACA,uBAAuB,0BAAK;AAC5B;AACA;AACA,KAAK,EAAE,cAAc,CAAC,sBAAc;AACpC;AACA,0BAA0B,cAAc;;AAExC;AACA;AACA;AACA;;AAEA;AACA;AACA,qBAAqB,+BAAa;AAClC;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,+BAAa;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,qCAAqC,MAAM;;AAE3C;AACA;AACA,MAAM;AACN;AACA,kCAAkC,mBAAmB;AACrD;AACA,gCAAgC,cAAc;AAC9C,8BAA8B,YAAY;AAC1C,kCAAkC,gBAAgB;AAClD,8BAA8B,iBAAiB;AAC/C,8BAA8B,YAAY;AAC1C;AACA;AACA;AACA;AACA,MAAM,uCAAiB;AACvB;AACA;;AAEA;AACA;AACA,cAAc,aAAa;AAC3B,cAAc,eAAe;AAC7B;AACA;AACA,gBAAgB,MAAM;AACtB,kBAAkB,QAAQ;AAC1B,kBAAkB,QAAQ;AAC1B;AACA,UAAU,QAAQ;AAClB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB;AACA;AACA,WAAW,kCAAU,uBAAuB,OAAO,UAAU,UAAU;AACvE;;AAEA;AACA;AACA,cAAc,oBAAoB;AAClC;AACA,cAAc,eAAe;AAC7B;AACA;AACA,mBAAmB,oBAAoB;AACvC,kBAAkB,QAAQ;AAC1B,kBAAkB,QAAQ;AAC1B;AACA,UAAU,aAAa;AACvB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB;AACA;AACA,WAAW,iCAAS,0BAA0B,OAAO,UAAU,UAAU;AACzE;;AAEA;AACA,YAAY,QAAQ;AACpB;AACA;AACA;AACA,WAAW,OAAO;AAClB;;AAEA;AACA,YAAY,SAAS;AACrB;AACA;AACA;AACA;;AAEA;AACA,YAAY,gBAAgB;AAC5B;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA,uBAAuB,cAAc;AACrC;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,cAAc,YAAY;AAC1B;AACA;AACA;AACA,IAAI,yCAAmB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,QAAQ;AACtB,cAAc,6BAA6B;AAC3C;AACA;AACA;AACA;AACA,gBAAgB,kCAAU;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,+BAA+B;AAC5C;AACA,aAAa,QAAQ;AACrB,eAAe,SAAS;AACxB;AACA,gBAAgB,SAAS;AACzB;AACA,kBAAkB,SAAS;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,4BAAU;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,QAAQ;AACtB,kBAAkB,SAAS;AAC3B;AACA;AACA;AACA;AACA;AACA;;AAEA,kBAAkB;AAClB,uBAAuB,+BAAa;AACpC;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB;AACA,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,cAAc,QAAQ;AACtB,gBAAgB,SAAS;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,cAAc;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;ACte2C;AACL;;AAEtC;AACO;AACP;AACO,qCAAqC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA,OAAO,IAAI;;AAEX;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,EAAE,yDAAK;AACd,KAAK;AACL;AACA;AACA,yBAAyB,kBAAkB,EAAE,iBAAiB;AAC9D,OAAO;AACP;AACA,wBAAwB,+DAAU;AAClC,KAAK;AACL;AACA;;AAEO;AACP;AACA,iBAAiB,6DAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,yDAAK;AAChB;;AAEA;AACA;AACA,qCAAqC,6DAAS;AAC9C;;AAEA;AACA;;AAEO;AACP;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;;;;;;;;;;;;;;;;;;;ACxLO;AACP;AACA;;AAEO;AACP;AACA;;AAEA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;;AAEO;AACP;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACO;AACP;AACA;AACA;;AAEA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACO;AACP;AACA;AACA;AACA;;;;;;;;;;;;;ACjFA;AACA,OAAO,uCAAuC;;AAE9C;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM,sBAAsB;AAC5B;AACA,MAAM,8BAA8B;AACpC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;;AAEA;AACO;AACP;AACA;AACA;;;;;;;;;;;;;;;;;;ACnEO;AACP;AACA;;AAEO;AACP;AACA;;AAEA;AACO;AACP;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEO;AACP;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO,yDAAyD;AAChE;AACA;AACA;AACA;AACA;AACA,cAAc,KAAK,EAAE,KAAK,IAAI,IAAI;AAClC,GAAG;AACH,cAAc,WAAW,KAAK,QAAQ;;AAEtC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACO;AACP;AACA;;;;;;;;;;;AC5DA;IAII,gBAAY,SAAiB,EAAE,cAAoC;QAApC,oDAAoC;QAC/D,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;IAC1C,CAAC;IAED,qBAAI,GAAJ;QAAA,iBAMC;QALG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,UAAC,aAAa;YACvC,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;gBAC/B,MAAM,CAAC,gBAAgB,CAAC,KAAI,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;aAC3D;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IACL,aAAC;AAAD,CAAC;AAED,qBAAe,MAAM,CAAC;;;;;;;UClBtB;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA,8CAA8C;;;;;WCA9C;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;ACN+C;AACS;AACU;AAC/B;AAEnC,IAAMC,eAAe,GAAG,IAAInB,mDAAM,CAAC,YAAY,EAAE,CAACa,wDAAe,CAAC,CAAC;AACnE,IAAMO,oBAAoB,GAAG,IAAIpB,mDAAM,CAAC,kBAAkB,EAAE,CAACa,wDAAe,CAAC,CAAC;AAE9EM,eAAe,CAACD,IAAI,EAAE;AACtBE,oBAAoB,CAACF,IAAI,EAAE;AAE3B,+DAAe;EACXpB,UAAU,EAAVA,+EAAU;EACVC,eAAe,EAAfA,oFAAeA;AACnB,CAAC,E", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/plugins/datepicker.js", "webpack:///./node_modules/flowbite-datepicker/js/DateRangePicker.js", "webpack:///./node_modules/flowbite-datepicker/js/i18n/base-locales.js", "webpack:///./node_modules/flowbite-datepicker/js/options/defaultOptions.js", "webpack:///./node_modules/flowbite-datepicker/js/lib/dom.js", "webpack:///./node_modules/flowbite-datepicker/js/options/processOptions.js", "webpack:///./node_modules/flowbite-datepicker/js/picker/templates/pickerTemplate.js", "webpack:///./node_modules/flowbite-datepicker/js/picker/templates/daysTemplate.js", "webpack:///./node_modules/flowbite-datepicker/js/picker/templates/calendarWeeksTemplate.js", "webpack:///./node_modules/flowbite-datepicker/js/picker/views/View.js", "webpack:///./node_modules/flowbite-datepicker/js/picker/views/DaysView.js", "webpack:///./node_modules/flowbite-datepicker/js/picker/views/MonthsView.js", "webpack:///./node_modules/flowbite-datepicker/js/picker/views/YearsView.js", "webpack:///./node_modules/flowbite-datepicker/js/events/functions.js", "webpack:///./node_modules/flowbite-datepicker/js/events/pickerListeners.js", "webpack:///./node_modules/flowbite-datepicker/js/picker/Picker.js", "webpack:///./node_modules/flowbite-datepicker/js/events/inputFieldListeners.js", "webpack:///./node_modules/flowbite-datepicker/js/events/otherListeners.js", "webpack:///./node_modules/flowbite-datepicker/js/Datepicker.js", "webpack:///./node_modules/flowbite-datepicker/js/lib/date-format.js", "webpack:///./node_modules/flowbite-datepicker/js/lib/date.js", "webpack:///./node_modules/flowbite-datepicker/js/lib/event.js", "webpack:///./node_modules/flowbite-datepicker/js/lib/utils.js", "webpack:///./src/dom/events.ts", "webpack:///webpack/bootstrap", "webpack:///webpack/runtime/define property getters", "webpack:///webpack/runtime/hasOwnProperty shorthand", "webpack:///webpack/runtime/make namespace object", "webpack:///./src/plugins/datepicker.turbo.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"Flowbite\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Flowbite\"] = factory();\n\telse\n\t\troot[\"Flowbite\"] = factory();\n})(self, function() {\nreturn ", "import Datepicker from 'flowbite-datepicker/Datepicker';\nimport DateRangePicker from 'flowbite-datepicker/DateRangePicker';\nimport Events from '../dom/events';\n\nconst getDatepickerOptions = (datepickerEl) => {\n    const buttons = datepickerEl.hasAttribute('datepicker-buttons');\n    const autohide = datepickerEl.hasAttribute('datepicker-autohide');\n    const format = datepickerEl.hasAttribute('datepicker-format');\n    const orientation = datepickerEl.hasAttribute('datepicker-orientation');\n    const title = datepickerEl.hasAttribute('datepicker-title');\n\n    const options = {};\n    if (buttons) {\n        options.todayBtn = true;\n        options.clearBtn = true;\n    }\n    if (autohide) {\n        options.autohide = true;\n    }\n    if (format) {\n        options.format = datepickerEl.getAttribute('datepicker-format');\n    }\n    if (orientation) {\n        options.orientation = datepickerEl.getAttribute(\n            'datepicker-orientation'\n        );\n    }\n    if (title) {\n        options.title = datepickerEl.getAttribute('datepicker-title');\n    }\n\n    return options;\n};\n\nexport function initDatepickers() {\n    document.querySelectorAll('[datepicker]').forEach(function (datepickerEl) {\n        new Datepicker(datepickerEl, getDatepickerOptions(datepickerEl));\n    });\n\n    document\n        .querySelectorAll('[inline-datepicker]')\n        .forEach(function (datepickerEl) {\n            new Datepicker(datepickerEl, getDatepickerOptions(datepickerEl));\n        });\n\n    document\n        .querySelectorAll('[date-rangepicker]')\n        .forEach(function (datepickerEl) {\n            new DateRangePicker(\n                datepickerEl,\n                getDatepickerOptions(datepickerEl)\n            );\n        });\n}\n\nconst events = new Events('DOMContentLoaded', [initDatepickers]);\nevents.init();\n", "import {registerListeners, unregisterListeners} from './lib/event.js';\nimport {formatDate} from './lib/date-format.js';\nimport Datepicker from './Datepicker.js';\n\n// filter out the config options inapproprite to pass to Datepicker\nfunction filterOptions(options) {\n  const newOpts = Object.assign({}, options);\n\n  delete newOpts.inputs;\n  delete newOpts.allowOneSidedRange;\n  delete newOpts.maxNumberOfDates; // to ensure each datepicker handles a single date\n\n  return newOpts;\n}\n\nfunction setupDatepicker(rangepicker, changeDateListener, el, options) {\n  registerListeners(rangepicker, [\n    [el, 'changeDate', changeDateListener],\n  ]);\n  new Datepicker(el, options, rangepicker);\n}\n\nfunction onChangeDate(rangepicker, ev) {\n  // to prevent both datepickers trigger the other side's update each other\n  if (rangepicker._updating) {\n    return;\n  }\n  rangepicker._updating = true;\n\n  const target = ev.target;\n  if (target.datepicker === undefined) {\n    return;\n  }\n\n  const datepickers = rangepicker.datepickers;\n  const setDateOptions = {render: false};\n  const changedSide = rangepicker.inputs.indexOf(target);\n  const otherSide = changedSide === 0 ? 1 : 0;\n  const changedDate = datepickers[changedSide].dates[0];\n  const otherDate = datepickers[otherSide].dates[0];\n\n  if (changedDate !== undefined && otherDate !== undefined) {\n    // if the start of the range > the end, swap them\n    if (changedSide === 0 && changedDate > otherDate) {\n      datepickers[0].setDate(otherDate, setDateOptions);\n      datepickers[1].setDate(changedDate, setDateOptions);\n    } else if (changedSide === 1 && changedDate < otherDate) {\n      datepickers[0].setDate(changedDate, setDateOptions);\n      datepickers[1].setDate(otherDate, setDateOptions);\n    }\n  } else if (!rangepicker.allowOneSidedRange) {\n    // to prevent the range from becoming one-sided, copy changed side's\n    // selection (no matter if it's empty) to the other side\n    if (changedDate !== undefined || otherDate !== undefined) {\n      setDateOptions.clear = true;\n      datepickers[otherSide].setDate(datepickers[changedSide].dates, setDateOptions);\n    }\n  }\n  datepickers[0].picker.update().render();\n  datepickers[1].picker.update().render();\n  delete rangepicker._updating;\n}\n\n/**\n * Class representing a date range picker\n */\nexport default class DateRangePicker  {\n  /**\n   * Create a date range picker\n   * @param  {Element} element - element to bind a date range picker\n   * @param  {Object} [options] - config options\n   */\n  constructor(element, options = {}) {\n    const inputs = Array.isArray(options.inputs)\n      ? options.inputs\n      : Array.from(element.querySelectorAll('input'));\n    if (inputs.length < 2) {\n      return;\n    }\n\n    element.rangepicker = this;\n    this.element = element;\n    this.inputs = inputs.slice(0, 2);\n    this.allowOneSidedRange = !!options.allowOneSidedRange;\n\n    const changeDateListener = onChangeDate.bind(null, this);\n    const cleanOptions = filterOptions(options);\n    // in order for initial date setup to work right when pcicLvel > 0,\n    // let Datepicker constructor add the instance to the rangepicker\n    const datepickers = [];\n    Object.defineProperty(this, 'datepickers', {\n      get() {\n        return datepickers;\n      },\n    });\n    setupDatepicker(this, changeDateListener, this.inputs[0], cleanOptions);\n    setupDatepicker(this, changeDateListener, this.inputs[1], cleanOptions);\n    Object.freeze(datepickers);\n    // normalize the range if inital dates are given\n    if (datepickers[0].dates.length > 0) {\n      onChangeDate(this, {target: this.inputs[0]});\n    } else if (datepickers[1].dates.length > 0) {\n      onChangeDate(this, {target: this.inputs[1]});\n    }\n  }\n\n  /**\n   * @type {Array} - selected date of the linked date pickers\n   */\n  get dates() {\n    return this.datepickers.length === 2\n      ? [\n          this.datepickers[0].dates[0],\n          this.datepickers[1].dates[0],\n        ]\n      : undefined;\n  }\n\n  /**\n   * Set new values to the config options\n   * @param {Object} options - config options to update\n   */\n  setOptions(options) {\n    this.allowOneSidedRange = !!options.allowOneSidedRange;\n\n    const cleanOptions = filterOptions(options);\n    this.datepickers[0].setOptions(cleanOptions);\n    this.datepickers[1].setOptions(cleanOptions);\n  }\n\n  /**\n   * Destroy the DateRangePicker instance\n   * @return {DateRangePicker} - the instance destroyed\n   */\n  destroy() {\n    this.datepickers[0].destroy();\n    this.datepickers[1].destroy();\n    unregisterListeners(this);\n    delete this.element.rangepicker;\n  }\n\n  /**\n   * Get the start and end dates of the date range\n   *\n   * The method returns Date objects by default. If format string is passed,\n   * it returns date strings formatted in given format.\n   * The result array always contains 2 items (start date/end date) and\n   * undefined is used for unselected side. (e.g. If none is selected,\n   * the result will be [undefined, undefined]. If only the end date is set\n   * when allowOneSidedRange config option is true, [undefined, endDate] will\n   * be returned.)\n   *\n   * @param  {String} [format] - Format string to stringify the dates\n   * @return {Array} - Start and end dates\n   */\n  getDates(format = undefined) {\n    const callback = format\n      ? date => formatDate(date, format, this.datepickers[0].config.locale)\n      : date => new Date(date);\n\n    return this.dates.map(date => date === undefined ? date : callback(date));\n  }\n\n  /**\n   * Set the start and end dates of the date range\n   *\n   * The method calls datepicker.setDate() internally using each of the\n   * arguments in start→end order.\n   *\n   * When a clear: true option object is passed instead of a date, the method\n   * clears the date.\n   *\n   * If an invalid date, the same date as the current one or an option object\n   * without clear: true is passed, the method considers that argument as an\n   * \"ineffective\" argument because calling datepicker.setDate() with those\n   * values makes no changes to the date selection.\n   *\n   * When the allowOneSidedRange config option is false, passing {clear: true}\n   * to clear the range works only when it is done to the last effective\n   * argument (in other words, passed to rangeEnd or to rangeStart along with\n   * ineffective rangeEnd). This is because when the date range is changed,\n   * it gets normalized based on the last change at the end of the changing\n   * process.\n   *\n   * @param {Date|Number|String|Object} rangeStart - Start date of the range\n   * or {clear: true} to clear the date\n   * @param {Date|Number|String|Object} rangeEnd - End date of the range\n   * or {clear: true} to clear the date\n   */\n  setDates(rangeStart, rangeEnd) {\n    const [datepicker0, datepicker1] = this.datepickers;\n    const origDates = this.dates;\n\n    // If range normalization runs on every change, we can't set a new range\n    // that starts after the end of the current range correctly because the\n    // normalization process swaps start↔︎end right after setting the new start\n    // date. To prevent this, the normalization process needs to run once after\n    // both of the new dates are set.\n    this._updating = true;\n    datepicker0.setDate(rangeStart);\n    datepicker1.setDate(rangeEnd);\n    delete this._updating;\n\n    if (datepicker1.dates[0] !== origDates[1]) {\n      onChangeDate(this, {target: this.inputs[1]});\n    } else if (datepicker0.dates[0] !== origDates[0]) {\n      onChangeDate(this, {target: this.inputs[0]});\n    }\n  }\n}\n", "// default locales\nexport const locales = {\n  en: {\n    days: [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"],\n    daysShort: [\"Sun\", \"Mon\", \"<PERSON><PERSON>\", \"Wed\", \"<PERSON>hu\", \"<PERSON>i\", \"Sat\"],\n    daysMin: [\"Su\", \"<PERSON>\", \"Tu\", \"We\", \"Th\", \"Fr\", \"Sa\"],\n    months: [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"],\n    monthsShort: [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"],\n    today: \"Today\",\n    clear: \"Clear\",\n    titleFormat: \"MM y\"\n  }\n};\n", "// config options updatable by setOptions() and their default values\nconst defaultOptions = {\n  autohide: false,\n  beforeShowDay: null,\n  beforeShowDecade: null,\n  beforeShowMonth: null,\n  beforeShowYear: null,\n  calendarWeeks: false,\n  clearBtn: false,\n  dateDelimiter: ',',\n  datesDisabled: [],\n  daysOfWeekDisabled: [],\n  daysOfWeekHighlighted: [],\n  defaultViewDate: undefined, // placeholder, defaults to today() by the program\n  disableTouchKeyboard: false,\n  format: 'mm/dd/yyyy',\n  language: 'en',\n  maxDate: null,\n  maxNumberOfDates: 1,\n  maxView: 3,\n  minDate: null,\n  nextArrow: '<svg class=\"w-4 h-4 rtl:rotate-180 text-gray-800 dark:text-white\" aria-hidden=\"true\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 14 10\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M1 5h12m0 0L9 1m4 4L9 9\"/></svg>',\n  orientation: 'auto',\n  pickLevel: 0,\n  prevArrow: '<svg class=\"w-4 h-4 rtl:rotate-180 text-gray-800 dark:text-white\" aria-hidden=\"true\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 14 10\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 5H1m0 0 4 4M1 5l4-4\"/></svg>',\n  showDaysOfWeek: true,\n  showOnClick: true,\n  showOnFocus: true,\n  startView: 0,\n  title: '',\n  todayBtn: false,\n  todayBtnMode: 0,\n  todayHighlight: false,\n  updateOnBlur: true,\n  weekStart: 0,\n};\n\nexport default defaultOptions;\n", "const range = document.createRange();\n\nexport function parseHTML(html) {\n  return range.createContextualFragment(html);\n}\n\n// equivalent to jQuery's :visble\nexport function isVisible(el) {\n  return !!(el.offsetWidth || el.offsetHeight || el.getClientRects().length);\n}\n\nexport function hideElement(el) {\n  if (el.style.display === 'none') {\n    return;\n  }\n  // back up the existing display setting in data-style-display\n  if (el.style.display) {\n    el.dataset.styleDisplay = el.style.display;\n  }\n  el.style.display = 'none';\n}\n\nexport function showElement(el) {\n  if (el.style.display !== 'none') {\n    return;\n  }\n  if (el.dataset.styleDisplay) {\n    // restore backed-up dispay property\n    el.style.display = el.dataset.styleDisplay;\n    delete el.dataset.styleDisplay;\n  } else {\n    el.style.display = '';\n  }\n}\n\nexport function emptyChildNodes(el) {\n  if (el.firstChild) {\n    el.removeChild(el.firstChild);\n    emptyChildNodes(el);\n  }\n}\n\nexport function replaceChildNodes(el, newChildNodes) {\n  emptyChildNodes(el);\n  if (newChildNodes instanceof DocumentFragment) {\n    el.appendChild(newChildNodes);\n  } else if (typeof newChildNodes === 'string') {\n    el.appendChild(parseHTML(newChildNodes));\n  } else if (typeof newChildNodes.forEach === 'function') {\n    newChildNodes.forEach((node) => {\n      el.appendChild(node);\n    });\n  }\n}\n", "import {hasProperty, pushUnique} from '../lib/utils.js';\nimport {dateValue} from '../lib/date.js';\nimport {reFormatTokens, parseDate} from '../lib/date-format.js';\nimport {parseHTML} from '../lib/dom.js';\nimport defaultOptions from './defaultOptions.js';\n\nconst {\n  language: defaultLang,\n  format: defaultFormat,\n  weekStart: defaultWeekStart,\n} = defaultOptions;\n\n// Reducer function to filter out invalid day-of-week from the input\nfunction sanitizeDOW(dow, day) {\n  return dow.length < 6 && day >= 0 && day < 7\n    ? pushUnique(dow, day)\n    : dow;\n}\n\nfunction calcEndOfWeek(startOfWeek) {\n  return (startOfWeek + 6) % 7;\n}\n\n// validate input date. if invalid, fallback to the original value\nfunction validateDate(value, format, locale, origValue) {\n  const date = parseDate(value, format, locale);\n  return date !== undefined ? date : origValue;\n}\n\n// Validate viewId. if invalid, fallback to the original value\nfunction validateViewId(value, origValue, max = 3) {\n  const viewId = parseInt(value, 10);\n  return viewId >= 0 && viewId <= max ? viewId : origValue;\n}\n\n// Create Datepicker configuration to set\nexport default function processOptions(options, datepicker) {\n  const inOpts = Object.assign({}, options);\n  const config = {};\n  const locales = datepicker.constructor.locales;\n  let {\n    format,\n    language,\n    locale,\n    maxDate,\n    maxView,\n    minDate,\n    pickLevel,\n    startView,\n    weekStart,\n  } = datepicker.config || {};\n\n  if (inOpts.language) {\n    let lang;\n    if (inOpts.language !== language) {\n      if (locales[inOpts.language]) {\n        lang = inOpts.language;\n      } else {\n        // Check if langauge + region tag can fallback to the one without\n        // region (e.g. fr-CA → fr)\n        lang = inOpts.language.split('-')[0];\n        if (locales[lang] === undefined) {\n          lang = false;\n        }\n      }\n    }\n    delete inOpts.language;\n    if (lang) {\n      language = config.language = lang;\n\n      // update locale as well when updating language\n      const origLocale = locale || locales[defaultLang];\n      // use default language's properties for the fallback\n      locale = Object.assign({\n        format: defaultFormat,\n        weekStart: defaultWeekStart\n      }, locales[defaultLang]);\n      if (language !== defaultLang) {\n        Object.assign(locale, locales[language]);\n      }\n      config.locale = locale;\n      // if format and/or weekStart are the same as old locale's defaults,\n      // update them to new locale's defaults\n      if (format === origLocale.format) {\n        format = config.format = locale.format;\n      }\n      if (weekStart === origLocale.weekStart) {\n        weekStart = config.weekStart = locale.weekStart;\n        config.weekEnd = calcEndOfWeek(locale.weekStart);\n      }\n    }\n  }\n\n  if (inOpts.format) {\n    const hasToDisplay = typeof inOpts.format.toDisplay === 'function';\n    const hasToValue = typeof inOpts.format.toValue === 'function';\n    const validFormatString = reFormatTokens.test(inOpts.format);\n    if ((hasToDisplay && hasToValue) || validFormatString) {\n      format = config.format = inOpts.format;\n    }\n    delete inOpts.format;\n  }\n\n  //*** dates ***//\n  // while min and maxDate for \"no limit\" in the options are better to be null\n  // (especially when updating), the ones in the config have to be undefined\n  // because null is treated as 0 (= unix epoch) when comparing with time value\n  let minDt = minDate;\n  let maxDt = maxDate;\n  if (inOpts.minDate !== undefined) {\n    minDt = inOpts.minDate === null\n      ? dateValue(0, 0, 1)  // set 0000-01-01 to prevent negative values for year\n      : validateDate(inOpts.minDate, format, locale, minDt);\n    delete inOpts.minDate;\n  }\n  if (inOpts.maxDate !== undefined) {\n    maxDt = inOpts.maxDate === null\n      ? undefined\n      : validateDate(inOpts.maxDate, format, locale, maxDt);\n    delete inOpts.maxDate;\n  }\n  if (maxDt < minDt) {\n    minDate = config.minDate = maxDt;\n    maxDate = config.maxDate = minDt;\n  } else {\n    if (minDate !== minDt) {\n      minDate = config.minDate = minDt;\n    }\n    if (maxDate !== maxDt) {\n      maxDate = config.maxDate = maxDt;\n    }\n  }\n\n  if (inOpts.datesDisabled) {\n    config.datesDisabled = inOpts.datesDisabled.reduce((dates, dt) => {\n      const date = parseDate(dt, format, locale);\n      return date !== undefined ? pushUnique(dates, date) : dates;\n    }, []);\n    delete inOpts.datesDisabled;\n  }\n  if (inOpts.defaultViewDate !== undefined) {\n    const viewDate = parseDate(inOpts.defaultViewDate, format, locale);\n    if (viewDate !== undefined) {\n      config.defaultViewDate = viewDate;\n    }\n    delete inOpts.defaultViewDate;\n  }\n\n  //*** days of week ***//\n  if (inOpts.weekStart !== undefined) {\n    const wkStart = Number(inOpts.weekStart) % 7;\n    if (!isNaN(wkStart)) {\n      weekStart = config.weekStart = wkStart;\n      config.weekEnd = calcEndOfWeek(wkStart);\n    }\n    delete inOpts.weekStart;\n  }\n  if (inOpts.daysOfWeekDisabled) {\n    config.daysOfWeekDisabled = inOpts.daysOfWeekDisabled.reduce(sanitizeDOW, []);\n    delete inOpts.daysOfWeekDisabled;\n  }\n  if (inOpts.daysOfWeekHighlighted) {\n    config.daysOfWeekHighlighted = inOpts.daysOfWeekHighlighted.reduce(sanitizeDOW, []);\n    delete inOpts.daysOfWeekHighlighted;\n  }\n\n  //*** multi date ***//\n  if (inOpts.maxNumberOfDates !== undefined) {\n    const maxNumberOfDates = parseInt(inOpts.maxNumberOfDates, 10);\n    if (maxNumberOfDates >= 0) {\n      config.maxNumberOfDates = maxNumberOfDates;\n      config.multidate = maxNumberOfDates !== 1;\n    }\n    delete inOpts.maxNumberOfDates;\n  }\n  if (inOpts.dateDelimiter) {\n    config.dateDelimiter = String(inOpts.dateDelimiter);\n    delete inOpts.dateDelimiter;\n  }\n\n  //*** pick level & view ***//\n  let newPickLevel = pickLevel;\n  if (inOpts.pickLevel !== undefined) {\n    newPickLevel = validateViewId(inOpts.pickLevel, 2);\n    delete inOpts.pickLevel;\n  }\n  if (newPickLevel !== pickLevel) {\n    pickLevel = config.pickLevel = newPickLevel;\n  }\n\n  let newMaxView = maxView;\n  if (inOpts.maxView !== undefined) {\n    newMaxView = validateViewId(inOpts.maxView, maxView);\n    delete inOpts.maxView;\n  }\n  // ensure max view >= pick level\n  newMaxView = pickLevel > newMaxView ? pickLevel : newMaxView;\n  if (newMaxView !== maxView) {\n    maxView = config.maxView = newMaxView;\n  }\n\n  let newStartView = startView;\n  if (inOpts.startView !== undefined) {\n    newStartView = validateViewId(inOpts.startView, newStartView);\n    delete inOpts.startView;\n  }\n  // ensure pick level <= start view <= max view\n  if (newStartView < pickLevel) {\n    newStartView = pickLevel;\n  } else if (newStartView > maxView) {\n    newStartView = maxView;\n  }\n  if (newStartView !== startView) {\n    config.startView = newStartView;\n  }\n\n  //*** template ***//\n  if (inOpts.prevArrow) {\n    const prevArrow = parseHTML(inOpts.prevArrow);\n    if (prevArrow.childNodes.length > 0) {\n      config.prevArrow = prevArrow.childNodes;\n    }\n    delete inOpts.prevArrow;\n  }\n  if (inOpts.nextArrow) {\n    const nextArrow = parseHTML(inOpts.nextArrow);\n    if (nextArrow.childNodes.length > 0) {\n      config.nextArrow = nextArrow.childNodes;\n    }\n    delete inOpts.nextArrow;\n  }\n\n  //*** misc ***//\n  if (inOpts.disableTouchKeyboard !== undefined) {\n    config.disableTouchKeyboard = 'ontouchstart' in document && !!inOpts.disableTouchKeyboard;\n    delete inOpts.disableTouchKeyboard;\n  }\n  if (inOpts.orientation) {\n    const orientation = inOpts.orientation.toLowerCase().split(/\\s+/g);\n    config.orientation = {\n      x: orientation.find(x => (x === 'left' || x === 'right')) || 'auto',\n      y: orientation.find(y => (y === 'top' || y === 'bottom')) || 'auto',\n    };\n    delete inOpts.orientation;\n  }\n  if (inOpts.todayBtnMode !== undefined) {\n    switch(inOpts.todayBtnMode) {\n      case 0:\n      case 1:\n        config.todayBtnMode = inOpts.todayBtnMode;\n    }\n    delete inOpts.todayBtnMode;\n  }\n\n  //*** copy the rest ***//\n  Object.keys(inOpts).forEach((key) => {\n    if (inOpts[key] !== undefined && hasProperty(defaultOptions, key)) {\n      config[key] = inOpts[key];\n    }\n  });\n\n  return config;\n}\n", "import {optimizeTemplateHTML} from '../../lib/utils.js';\n\nconst pickerTemplate = optimizeTemplateHTML(`<div class=\"datepicker hidden\">\n  <div class=\"datepicker-picker inline-block rounded-lg bg-white dark:bg-gray-700 shadow-lg p-4\">\n    <div class=\"datepicker-header\">\n      <div class=\"datepicker-title bg-white dark:bg-gray-700 dark:text-white px-2 py-3 text-center font-semibold\"></div>\n      <div class=\"datepicker-controls flex justify-between mb-2\">\n        <button type=\"button\" class=\"bg-white dark:bg-gray-700 rounded-lg text-gray-500 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-white text-lg p-2.5 focus:outline-none focus:ring-2 focus:ring-gray-200 prev-btn\"></button>\n        <button type=\"button\" class=\"text-sm rounded-lg text-gray-900 dark:text-white bg-white dark:bg-gray-700 font-semibold py-2.5 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-200 view-switch\"></button>\n        <button type=\"button\" class=\"bg-white dark:bg-gray-700 rounded-lg text-gray-500 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-white text-lg p-2.5 focus:outline-none focus:ring-2 focus:ring-gray-200 next-btn\"></button>\n      </div>\n    </div>\n    <div class=\"datepicker-main p-1\"></div>\n    <div class=\"datepicker-footer\">\n      <div class=\"datepicker-controls flex space-x-2 mt-2\">\n        <button type=\"button\" class=\"%buttonClass% today-btn text-white bg-blue-700 !bg-primary-700 dark:bg-blue-600 dark:!bg-primary-600 hover:bg-blue-800 hover:!bg-primary-800 dark:hover:bg-blue-700 dark:hover:!bg-primary-700 focus:ring-4 focus:ring-blue-300 focus:!ring-primary-300 font-medium rounded-lg text-sm px-5 py-2 text-center w-1/2\"></button>\n        <button type=\"button\" class=\"%buttonClass% clear-btn text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 focus:ring-4 focus:ring-blue-300 focus:!ring-primary-300 font-medium rounded-lg text-sm px-5 py-2 text-center w-1/2\"></button>\n      </div>\n    </div>\n  </div>\n</div>`);\n\nexport default pickerTemplate;\n", "import {createTagRepeat, optimizeTemplateHTML} from '../../lib/utils.js';\n\nconst daysTemplate = optimizeTemplateHTML(`<div class=\"days\">\n  <div class=\"days-of-week grid grid-cols-7 mb-1\">${createTagRepeat('span', 7, {class: 'dow block flex-1 leading-9 border-0 rounded-lg cursor-default text-center text-gray-900 font-semibold text-sm'})}</div>\n  <div class=\"datepicker-grid w-64 grid grid-cols-7\">${createTagRepeat('span', 42 , {class: 'block flex-1 leading-9 border-0 rounded-lg cursor-default text-center text-gray-900 font-semibold text-sm h-6 leading-6 text-sm font-medium text-gray-500 dark:text-gray-400'})}</div>\n</div>`);\n\nexport default daysTemplate;\n", "import {createTagRepeat, optimizeTemplateHTML} from '../../lib/utils.js';\n\nconst calendarWeeksTemplate = optimizeTemplateHTML(`<div class=\"calendar-weeks\">\n  <div class=\"days-of-week flex\"><span class=\"dow h-6 leading-6 text-sm font-medium text-gray-500 dark:text-gray-400\"></span></div>\n  <div class=\"weeks\">${createTagRepeat('span', 6, {class: 'week block flex-1 leading-9 border-0 rounded-lg cursor-default text-center text-gray-900 font-semibold text-sm'})}</div>\n</div>`);\n\nexport default calendarWeeksTemplate;\n", "import {pushUnique} from '../../lib/utils.js';\nimport {parseHTML, replaceChildNodes} from '../../lib/dom.js';\n\n// Base class of the view classes\nexport default class View {\n  constructor(picker, config) {\n    Object.assign(this, config, {\n      picker,\n      element: parseHTML(`<div class=\"datepicker-view flex\"></div>`).firstChild,\n      selected: [],\n    });\n    this.init(this.picker.datepicker.config);\n  }\n\n  init(options) {\n    if (options.pickLevel !== undefined) {\n      this.isMinView = this.id === options.pickLevel;\n    }\n    this.setOptions(options);\n    this.updateFocus();\n    this.updateSelection();\n  }\n\n  // Execute beforeShow() callback and apply the result to the element\n  // args:\n  // - current - current value on the iteration on view rendering\n  // - timeValue - time value of the date to pass to beforeShow()\n  performBeforeHook(el, current, timeValue) {\n    let result = this.beforeShow(new Date(timeValue));\n    switch (typeof result) {\n      case 'boolean':\n        result = {enabled: result};\n        break;\n      case 'string':\n        result = {classes: result};\n    }\n\n    if (result) {\n      if (result.enabled === false) {\n        el.classList.add('disabled');\n        pushUnique(this.disabled, current);\n      }\n      if (result.classes) {\n        const extraClasses = result.classes.split(/\\s+/);\n        el.classList.add(...extraClasses);\n        if (extraClasses.includes('disabled')) {\n          pushUnique(this.disabled, current);\n        }\n      }\n      if (result.content) {\n        replaceChildNodes(el, result.content);\n      }\n    }\n  }\n}\n", "import {hasProperty, pushUnique} from '../../lib/utils.js';\nimport {today, dateValue, addDays, addWeeks, dayOfTheWeekOf, getWeek} from '../../lib/date.js';\nimport {formatDate} from '../../lib/date-format.js';\nimport {parseHTML, showElement, hideElement} from '../../lib/dom.js';\nimport daysTemplate from '../templates/daysTemplate.js';\nimport calendarWeeksTemplate from '../templates/calendarWeeksTemplate.js';\nimport View from './View.js';\n\nexport default class DaysView extends View {\n  constructor(picker) {\n    super(picker, {\n      id: 0,\n      name: 'days',\n      cellClass: 'day',\n    });\n  }\n\n  init(options, onConstruction = true) {\n    if (onConstruction) {\n      const inner = parseHTML(daysTemplate).firstChild;\n      this.dow = inner.firstChild;\n      this.grid = inner.lastChild;\n      this.element.appendChild(inner);\n    }\n    super.init(options);\n  }\n\n  setOptions(options) {\n    let updateDOW;\n\n    if (hasProperty(options, 'minDate')) {\n      this.minDate = options.minDate;\n    }\n    if (hasProperty(options, 'maxDate')) {\n      this.maxDate = options.maxDate;\n    }\n    if (options.datesDisabled) {\n      this.datesDisabled = options.datesDisabled;\n    }\n    if (options.daysOfWeekDisabled) {\n      this.daysOfWeekDisabled = options.daysOfWeekDisabled;\n      updateDOW = true;\n    }\n    if (options.daysOfWeekHighlighted) {\n      this.daysOfWeekHighlighted = options.daysOfWeekHighlighted;\n    }\n    if (options.todayHighlight !== undefined) {\n      this.todayHighlight = options.todayHighlight;\n    }\n    if (options.weekStart !== undefined) {\n      this.weekStart = options.weekStart;\n      this.weekEnd = options.weekEnd;\n      updateDOW = true;\n    }\n    if (options.locale) {\n      const locale = this.locale = options.locale;\n      this.dayNames = locale.daysMin;\n      this.switchLabelFormat = locale.titleFormat;\n      updateDOW = true;\n    }\n    if (options.beforeShowDay !== undefined) {\n      this.beforeShow = typeof options.beforeShowDay === 'function'\n        ? options.beforeShowDay\n        : undefined;\n    }\n\n    if (options.calendarWeeks !== undefined) {\n      if (options.calendarWeeks && !this.calendarWeeks) {\n        const weeksElem = parseHTML(calendarWeeksTemplate).firstChild;\n        this.calendarWeeks = {\n          element: weeksElem,\n          dow: weeksElem.firstChild,\n          weeks: weeksElem.lastChild,\n        };\n        this.element.insertBefore(weeksElem, this.element.firstChild);\n      } else if (this.calendarWeeks && !options.calendarWeeks) {\n        this.element.removeChild(this.calendarWeeks.element);\n        this.calendarWeeks = null;\n      }\n    }\n    if (options.showDaysOfWeek !== undefined) {\n      if (options.showDaysOfWeek) {\n        showElement(this.dow);\n        if (this.calendarWeeks) {\n          showElement(this.calendarWeeks.dow);\n        }\n      } else {\n        hideElement(this.dow);\n        if (this.calendarWeeks) {\n          hideElement(this.calendarWeeks.dow);\n        }\n      }\n    }\n\n    // update days-of-week when locale, daysOfweekDisabled or weekStart is changed\n    if (updateDOW) {\n      Array.from(this.dow.children).forEach((el, index) => {\n        const dow = (this.weekStart + index) % 7;\n        el.textContent = this.dayNames[dow];\n        el.className = this.daysOfWeekDisabled.includes(dow) ? 'dow disabled text-center h-6 leading-6 text-sm font-medium text-gray-500 dark:text-gray-400 cursor-not-allowed' : 'dow text-center h-6 leading-6 text-sm font-medium text-gray-500 dark:text-gray-400';\n      });\n    }\n  }\n\n  // Apply update on the focused date to view's settings\n  updateFocus() {\n    const viewDate = new Date(this.picker.viewDate);\n    const viewYear = viewDate.getFullYear();\n    const viewMonth = viewDate.getMonth();\n    const firstOfMonth = dateValue(viewYear, viewMonth, 1);\n    const start = dayOfTheWeekOf(firstOfMonth, this.weekStart, this.weekStart);\n\n    this.first = firstOfMonth;\n    this.last = dateValue(viewYear, viewMonth + 1, 0);\n    this.start = start;\n    this.focused = this.picker.viewDate;\n  }\n\n  // Apply update on the selected dates to view's settings\n  updateSelection() {\n    const {dates, rangepicker} = this.picker.datepicker;\n    this.selected = dates;\n    if (rangepicker) {\n      this.range = rangepicker.dates;\n    }\n  }\n\n   // Update the entire view UI\n  render() {\n    // update today marker on ever render\n    this.today = this.todayHighlight ? today() : undefined;\n    // refresh disabled dates on every render in order to clear the ones added\n    // by beforeShow hook at previous render\n    this.disabled = [...this.datesDisabled];\n\n    const switchLabel = formatDate(this.focused, this.switchLabelFormat, this.locale);\n    this.picker.setViewSwitchLabel(switchLabel);\n    this.picker.setPrevBtnDisabled(this.first <= this.minDate);\n    this.picker.setNextBtnDisabled(this.last >= this.maxDate);\n\n    if (this.calendarWeeks) {\n      // start of the UTC week (Monday) of the 1st of the month\n      const startOfWeek = dayOfTheWeekOf(this.first, 1, 1);\n      Array.from(this.calendarWeeks.weeks.children).forEach((el, index) => {\n        el.textContent = getWeek(addWeeks(startOfWeek, index));\n      });\n    }\n    Array.from(this.grid.children).forEach((el, index) => {\n      const classList = el.classList;\n      const current = addDays(this.start, index);\n      const date = new Date(current);\n      const day = date.getDay();\n\n      el.className = `datepicker-cell hover:bg-gray-100 dark:hover:bg-gray-600 block flex-1 leading-9 border-0 rounded-lg cursor-pointer text-center text-gray-900 dark:text-white font-semibold text-sm ${this.cellClass}`;\n      el.dataset.date = current;\n      el.textContent = date.getDate();\n\n      if (current < this.first) {\n        classList.add('prev', 'text-gray-500', 'dark:text-white');\n      } else if (current > this.last) {\n        classList.add('next', 'text-gray-500', 'dark:text-white');\n      }\n      if (this.today === current) {\n        classList.add('today', 'bg-gray-100', 'dark:bg-gray-600');\n      }\n      if (current < this.minDate || current > this.maxDate || this.disabled.includes(current)) {\n        classList.add('disabled', 'cursor-not-allowed');\n      }\n      if (this.daysOfWeekDisabled.includes(day)) {\n        classList.add('disabled', 'cursor-not-allowed');\n        pushUnique(this.disabled, current);\n      }\n      if (this.daysOfWeekHighlighted.includes(day)) {\n        classList.add('highlighted');\n      }\n      if (this.range) {\n        const [rangeStart, rangeEnd] = this.range;\n        if (current > rangeStart && current < rangeEnd) {\n          classList.add('range', 'bg-gray-200', 'dark:bg-gray-600');\n          classList.remove('rounded-lg', 'rounded-l-lg', 'rounded-r-lg')\n        }\n        if (current === rangeStart) {\n          classList.add('range-start', 'bg-gray-100', 'dark:bg-gray-600', 'rounded-l-lg');\n          classList.remove('rounded-lg', 'rounded-r-lg');\n        }\n        if (current === rangeEnd) {\n          classList.add('range-end', 'bg-gray-100', 'dark:bg-gray-600', 'rounded-r-lg');\n          classList.remove('rounded-lg', 'rounded-l-lg');\n        }\n      }\n      if (this.selected.includes(current)) {\n        classList.add('selected', 'bg-blue-700', '!bg-primary-700', 'text-white', 'dark:bg-blue-600', 'dark:!bg-primary-600', 'dark:text-white');\n        classList.remove('text-gray-900', 'text-gray-500', 'hover:bg-gray-100', 'dark:text-white', 'dark:hover:bg-gray-600', 'dark:bg-gray-600', 'bg-gray-100', 'bg-gray-200');\n      }\n      if (current === this.focused) {\n        classList.add('focused');\n      }\n\n      if (this.beforeShow) {\n        this.performBeforeHook(el, current, current);\n      }\n    });\n  }\n\n  // Update the view UI by applying the changes of selected and focused items\n  refresh() {\n    const [rangeStart, rangeEnd] = this.range || [];\n    this.grid\n      .querySelectorAll('.range, .range-start, .range-end, .selected, .focused')\n      .forEach((el) => {\n        el.classList.remove('range', 'range-start', 'range-end', 'selected', 'bg-blue-700', '!bg-primary-700', 'text-white', 'dark:bg-blue-600', 'dark:!bg-primary-600', 'dark:text-white', 'focused');\n        el.classList.add('text-gray-900', 'rounded-lg', 'dark:text-white');\n      });\n    Array.from(this.grid.children).forEach((el) => {\n      const current = Number(el.dataset.date);\n      const classList = el.classList;\n      classList.remove('bg-gray-200', 'dark:bg-gray-600', 'rounded-l-lg', 'rounded-r-lg')\n      if (current > rangeStart && current < rangeEnd) {\n        classList.add('range', 'bg-gray-200', 'dark:bg-gray-600');\n        classList.remove('rounded-lg');\n      }\n      if (current === rangeStart) {\n        classList.add('range-start', 'bg-gray-200', 'dark:bg-gray-600', 'rounded-l-lg');\n        classList.remove('rounded-lg', 'rounded-r-lg');\n      }\n      if (current === rangeEnd) {\n        classList.add('range-end', 'bg-gray-200', 'dark:bg-gray-600', 'rounded-r-lg');\n        classList.remove('rounded-lg', 'rounded-l-lg');\n      }\n      if (this.selected.includes(current)) {\n        classList.add('selected', 'bg-blue-700', '!bg-primary-700', 'text-white', 'dark:bg-blue-600', 'dark:!bg-primary-600', 'dark:text-white');\n        classList.remove('text-gray-900', 'hover:bg-gray-100', 'dark:text-white', 'dark:hover:bg-gray-600', 'bg-gray-100', 'bg-gray-200', 'dark:bg-gray-600');\n      }\n      if (current === this.focused) {\n        classList.add('focused');\n      }\n    });\n  }\n\n  // Update the view UI by applying the change of focused item\n  refreshFocus() {\n    const index = Math.round((this.focused - this.start) / 86400000);\n    this.grid.querySelectorAll('.focused').forEach((el) => {\n      el.classList.remove('focused');\n    });\n    this.grid.children[index].classList.add('focused');\n  }\n}\n", "import {hasProperty, pushUnique, createTagRepeat} from '../../lib/utils.js';\nimport {dateValue} from '../../lib/date.js';\nimport {parseHTML} from '../../lib/dom.js';\nimport View from './View.js';\n\nfunction computeMonthRange(range, thisYear) {\n  if (!range || !range[0] || !range[1]) {\n    return;\n  }\n\n  const [[startY, startM], [endY, endM]] = range;\n  if (startY > thisYear || endY < thisYear) {\n    return;\n  }\n  return [\n    startY === thisYear ? startM : -1,\n    endY === thisYear ? endM : 12,\n  ];\n}\n\nexport default class MonthsView extends View {\n  constructor(picker) {\n    super(picker, {\n      id: 1,\n      name: 'months',\n      cellClass: 'month',\n    });\n  }\n\n  init(options, onConstruction = true) {\n    if (onConstruction) {\n      this.grid = this.element;\n      this.element.classList.add('months', 'datepicker-grid', 'w-64', 'grid', 'grid-cols-4');\n      this.grid.appendChild(parseHTML(createTagRepeat('span', 12, {'data-month': ix => ix})));\n    }\n    super.init(options);\n  }\n\n  setOptions(options) {\n    if (options.locale) {\n      this.monthNames = options.locale.monthsShort;\n    }\n    if (hasProperty(options, 'minDate')) {\n      if (options.minDate === undefined) {\n        this.minYear = this.minMonth = this.minDate = undefined;\n      } else {\n        const minDateObj = new Date(options.minDate);\n        this.minYear = minDateObj.getFullYear();\n        this.minMonth = minDateObj.getMonth();\n        this.minDate = minDateObj.setDate(1);\n      }\n    }\n    if (hasProperty(options, 'maxDate')) {\n      if (options.maxDate === undefined) {\n        this.maxYear = this.maxMonth = this.maxDate = undefined;\n      } else {\n        const maxDateObj = new Date(options.maxDate);\n        this.maxYear = maxDateObj.getFullYear();\n        this.maxMonth = maxDateObj.getMonth();\n        this.maxDate = dateValue(this.maxYear, this.maxMonth + 1, 0);\n      }\n    }\n    if (options.beforeShowMonth !== undefined) {\n      this.beforeShow = typeof options.beforeShowMonth === 'function'\n        ? options.beforeShowMonth\n        : undefined;\n    }\n  }\n\n  // Update view's settings to reflect the viewDate set on the picker\n  updateFocus() {\n    const viewDate = new Date(this.picker.viewDate);\n    this.year = viewDate.getFullYear();\n    this.focused = viewDate.getMonth();\n  }\n\n  // Update view's settings to reflect the selected dates\n  updateSelection() {\n    const {dates, rangepicker} = this.picker.datepicker;\n    this.selected = dates.reduce((selected, timeValue) => {\n      const date = new Date(timeValue);\n      const year = date.getFullYear();\n      const month = date.getMonth();\n      if (selected[year] === undefined) {\n        selected[year] = [month];\n      } else {\n        pushUnique(selected[year], month);\n      }\n      return selected;\n    }, {});\n    if (rangepicker && rangepicker.dates) {\n      this.range = rangepicker.dates.map(timeValue => {\n        const date = new Date(timeValue);\n        return isNaN(date) ? undefined : [date.getFullYear(), date.getMonth()];\n      });\n    }\n  }\n\n  // Update the entire view UI\n  render() {\n    // refresh disabled months on every render in order to clear the ones added\n    // by beforeShow hook at previous render\n    this.disabled = [];\n\n    this.picker.setViewSwitchLabel(this.year);\n    this.picker.setPrevBtnDisabled(this.year <= this.minYear);\n    this.picker.setNextBtnDisabled(this.year >= this.maxYear);\n\n    const selected = this.selected[this.year] || [];\n    const yrOutOfRange = this.year < this.minYear || this.year > this.maxYear;\n    const isMinYear = this.year === this.minYear;\n    const isMaxYear = this.year === this.maxYear;\n    const range = computeMonthRange(this.range, this.year);\n\n    Array.from(this.grid.children).forEach((el, index) => {\n      const classList = el.classList;\n      const date = dateValue(this.year, index, 1);\n\n      el.className = `datepicker-cell hover:bg-gray-100 dark:hover:bg-gray-600 block flex-1 leading-9 border-0 rounded-lg cursor-pointer text-center text-gray-900 dark:text-white font-semibold text-sm ${this.cellClass}`;\n      if (this.isMinView) {\n        el.dataset.date = date;\n      }\n      // reset text on every render to clear the custom content set\n      // by beforeShow hook at previous render\n      el.textContent = this.monthNames[index];\n\n      if (\n        yrOutOfRange\n        || isMinYear && index < this.minMonth\n        || isMaxYear && index > this.maxMonth\n      ) {\n        classList.add('disabled');\n      }\n      if (range) {\n        const [rangeStart, rangeEnd] = range;\n        if (index > rangeStart && index < rangeEnd) {\n          classList.add('range');\n        }\n        if (index === rangeStart) {\n          classList.add('range-start');\n        }\n        if (index === rangeEnd) {\n          classList.add('range-end');\n        }\n      }\n      if (selected.includes(index)) {\n        classList.add('selected', 'bg-blue-700', '!bg-primary-700', 'text-white', 'dark:bg-blue-600', 'dark:!bg-primary-600', 'dark:text-white');\n        classList.remove('text-gray-900', 'hover:bg-gray-100', 'dark:text-white', 'dark:hover:bg-gray-600');\n      }\n      if (index === this.focused) {\n        classList.add('focused');\n      }\n\n      if (this.beforeShow) {\n        this.performBeforeHook(el, index, date);\n      }\n    });\n  }\n\n  // Update the view UI by applying the changes of selected and focused items\n  refresh() {\n    const selected = this.selected[this.year] || [];\n    const [rangeStart, rangeEnd] = computeMonthRange(this.range, this.year) || [];\n    this.grid\n      .querySelectorAll('.range, .range-start, .range-end, .selected, .focused')\n      .forEach((el) => {\n        el.classList.remove('range', 'range-start', 'range-end', 'selected', 'bg-blue-700', '!bg-primary-700', 'dark:bg-blue-600', 'dark:!bg-primary-700', 'dark:text-white', 'text-white', 'focused');\n        el.classList.add('text-gray-900', 'hover:bg-gray-100', 'dark:text-white', 'dark:hover:bg-gray-600');\n      });\n    Array.from(this.grid.children).forEach((el, index) => {\n      const classList = el.classList;\n      if (index > rangeStart && index < rangeEnd) {\n        classList.add('range');\n      }\n      if (index === rangeStart) {\n        classList.add('range-start');\n      }\n      if (index === rangeEnd) {\n        classList.add('range-end');\n      }\n      if (selected.includes(index)) {\n        classList.add('selected', 'bg-blue-700', '!bg-primary-700', 'text-white', 'dark:bg-blue-600', 'dark:!bg-primary-600', 'dark:text-white');\n        classList.remove('text-gray-900', 'hover:bg-gray-100', 'dark:text-white', 'dark:hover:bg-gray-600');\n      }\n      if (index === this.focused) {\n        classList.add('focused');\n      }\n    });\n  }\n\n  // Update the view UI by applying the change of focused item\n  refreshFocus() {\n    this.grid.querySelectorAll('.focused').forEach((el) => {\n      el.classList.remove('focused');\n    });\n    this.grid.children[this.focused].classList.add('focused');\n  }\n}", "import {hasProperty, pushUnique, createTagRepeat} from '../../lib/utils.js';\nimport {dateValue, startOfYearPeriod} from '../../lib/date.js';\nimport {parseHTML} from '../../lib/dom.js';\nimport View from './View.js';\n\nfunction toTitleCase(word) {\n  return [...word].reduce((str, ch, ix) => str += ix ? ch : ch.toUpperCase(), '');\n}\n\n// Class representing the years and decades view elements\nexport default class YearsView extends View {\n  constructor(picker, config) {\n    super(picker, config);\n  }\n\n  init(options, onConstruction = true) {\n    if (onConstruction) {\n      this.navStep = this.step * 10;\n      this.beforeShowOption = `beforeShow${toTitleCase(this.cellClass)}`;\n      this.grid = this.element;\n      this.element.classList.add(this.name, 'datepicker-grid', 'w-64', 'grid', 'grid-cols-4');\n      this.grid.appendChild(parseHTML(createTagRepeat('span', 12)));\n    }\n    super.init(options);\n  }\n\n  setOptions(options) {\n    if (hasProperty(options, 'minDate')) {\n      if (options.minDate === undefined) {\n        this.minYear = this.minDate = undefined;\n      } else {\n        this.minYear = startOfYearPeriod(options.minDate, this.step);\n        this.minDate = dateValue(this.minYear, 0, 1);\n      }\n    }\n    if (hasProperty(options, 'maxDate')) {\n      if (options.maxDate === undefined) {\n        this.maxYear = this.maxDate = undefined;\n      } else {\n        this.maxYear = startOfYearPeriod(options.maxDate, this.step);\n        this.maxDate = dateValue(this.maxYear, 11, 31);\n      }\n    }\n    if (options[this.beforeShowOption] !== undefined) {\n      const beforeShow = options[this.beforeShowOption];\n      this.beforeShow = typeof beforeShow === 'function' ? beforeShow : undefined;\n    }\n  }\n\n  // Update view's settings to reflect the viewDate set on the picker\n  updateFocus() {\n    const viewDate = new Date(this.picker.viewDate);\n    const first = startOfYearPeriod(viewDate, this.navStep);\n    const last = first + 9 * this.step;\n\n    this.first = first;\n    this.last = last;\n    this.start = first - this.step;\n    this.focused = startOfYearPeriod(viewDate, this.step);\n  }\n\n  // Update view's settings to reflect the selected dates\n  updateSelection() {\n    const {dates, rangepicker} = this.picker.datepicker;\n    this.selected = dates.reduce((years, timeValue) => {\n      return pushUnique(years, startOfYearPeriod(timeValue, this.step));\n    }, []);\n    if (rangepicker && rangepicker.dates) {\n      this.range = rangepicker.dates.map(timeValue => {\n        if (timeValue !== undefined) {\n          return startOfYearPeriod(timeValue, this.step);\n        }\n      });\n    }\n  }\n\n  // Update the entire view UI\n  render() {\n    // refresh disabled years on every render in order to clear the ones added\n    // by beforeShow hook at previous render\n    this.disabled = [];\n\n    this.picker.setViewSwitchLabel(`${this.first}-${this.last}`);\n    this.picker.setPrevBtnDisabled(this.first <= this.minYear);\n    this.picker.setNextBtnDisabled(this.last >= this.maxYear);\n\n    Array.from(this.grid.children).forEach((el, index) => {\n      const classList = el.classList;\n      const current = this.start + (index * this.step);\n      const date = dateValue(current, 0, 1);\n\n      el.className = `datepicker-cell hover:bg-gray-100 dark:hover:bg-gray-600 block flex-1 leading-9 border-0 rounded-lg cursor-pointer text-center text-gray-900 dark:text-white font-semibold text-sm ${this.cellClass}`;\n      if (this.isMinView) {\n        el.dataset.date = date;\n      }\n      el.textContent = el.dataset.year = current;\n\n      if (index === 0) {\n        classList.add('prev');\n      } else if (index === 11) {\n        classList.add('next');\n      }\n      if (current < this.minYear || current > this.maxYear) {\n        classList.add('disabled');\n      }\n      if (this.range) {\n        const [rangeStart, rangeEnd] = this.range;\n        if (current > rangeStart && current < rangeEnd) {\n          classList.add('range');\n        }\n        if (current === rangeStart) {\n          classList.add('range-start');\n        }\n        if (current === rangeEnd) {\n          classList.add('range-end');\n        }\n      }\n      if (this.selected.includes(current)) {\n        classList.add('selected', 'bg-blue-700', '!bg-primary-700', 'text-white', 'dark:bg-blue-600', 'dark:!bg-primary-600', 'dark:text-white');\n        classList.remove('text-gray-900', 'hover:bg-gray-100', 'dark:text-white', 'dark:hover:bg-gray-600');\n      }\n      if (current === this.focused) {\n        classList.add('focused');\n      }\n\n      if (this.beforeShow) {\n        this.performBeforeHook(el, current, date);\n      }\n    });\n  }\n\n  // Update the view UI by applying the changes of selected and focused items\n  refresh() {\n    const [rangeStart, rangeEnd] = this.range || [];\n    this.grid\n      .querySelectorAll('.range, .range-start, .range-end, .selected, .focused')\n      .forEach((el) => {\n        el.classList.remove('range', 'range-start', 'range-end', 'selected', 'bg-blue-700', '!bg-primary-700', 'text-white', 'dark:bg-blue-600', 'dark!bg-primary-600', 'dark:text-white', 'focused');\n      });\n    Array.from(this.grid.children).forEach((el) => {\n      const current = Number(el.textContent);\n      const classList = el.classList;\n      if (current > rangeStart && current < rangeEnd) {\n        classList.add('range');\n      }\n      if (current === rangeStart) {\n        classList.add('range-start');\n      }\n      if (current === rangeEnd) {\n        classList.add('range-end');\n      }\n      if (this.selected.includes(current)) {\n        classList.add('selected', 'bg-blue-700', '!bg-primary-700', 'text-white', 'dark:bg-blue-600', 'dark:!bg-primary-600', 'dark:text-white');\n        classList.remove('text-gray-900', 'hover:bg-gray-100', 'dark:text-white', 'dark:hover:bg-gray-600');\n      }\n      if (current === this.focused) {\n        classList.add('focused');\n      }\n    });\n  }\n\n  // Update the view UI by applying the change of focused item\n  refreshFocus() {\n    const index = Math.round((this.focused - this.start) / this.step);\n    this.grid.querySelectorAll('.focused').forEach((el) => {\n      el.classList.remove('focused');\n    });\n    this.grid.children[index].classList.add('focused');\n  }\n}\n", "import {limitToRange} from '../lib/utils.js';\nimport {addMonths, addYears} from '../lib/date.js';\n\nexport function triggerDatepickerEvent(datepicker, type) {\n  const detail = {\n    date: datepicker.getDate(),\n    viewDate: new Date(datepicker.picker.viewDate),\n    viewId: datepicker.picker.currentView.id,\n    datepicker,\n  };\n  datepicker.element.dispatchEvent(new CustomEvent(type, {detail}));\n}\n\n// direction: -1 (to previous), 1 (to next)\nexport function goToPrevOrNext(datepicker, direction) {\n  const {minDate, maxDate} = datepicker.config;\n  const {currentView, viewDate} = datepicker.picker;\n  let newViewDate;\n  switch (currentView.id) {\n    case 0:\n      newViewDate = addMonths(viewDate, direction);\n      break;\n    case 1:\n      newViewDate = addYears(viewDate, direction);\n      break;\n    default:\n      newViewDate = addYears(viewDate, direction * currentView.navStep);\n  }\n  newViewDate = limitToRange(newViewDate, minDate, maxDate);\n  datepicker.picker.changeFocus(newViewDate).render();\n}\n\nexport function switchView(datepicker) {\n  const viewId = datepicker.picker.currentView.id;\n  if (viewId === datepicker.config.maxView) {\n    return;\n  }\n  datepicker.picker.changeView(viewId + 1).render();\n}\n\nexport function unfocus(datepicker) {\n  if (datepicker.config.updateOnBlur) {\n    datepicker.update({autohide: true});\n  } else {\n    datepicker.refresh('input');\n    datepicker.hide();\n  }\n}\n", "import {today, addMonths, addYears} from '../lib/date.js';\nimport {findElementInEventPath} from '../lib/event.js';\nimport {goToPrevOrNext, switchView} from './functions.js';\n\nfunction goToSelectedMonthOrYear(datepicker, selection) {\n  const picker = datepicker.picker;\n  const viewDate = new Date(picker.viewDate);\n  const viewId = picker.currentView.id;\n  const newDate = viewId === 1\n    ? addMonths(viewDate, selection - viewDate.getMonth())\n    : addYears(viewDate, selection - viewDate.getFullYear());\n\n  picker.changeFocus(newDate).changeView(viewId - 1).render();\n}\n\nexport function onClickTodayBtn(datepicker) {\n  const picker = datepicker.picker;\n  const currentDate = today();\n  if (datepicker.config.todayBtnMode === 1) {\n    if (datepicker.config.autohide) {\n      datepicker.setDate(currentDate);\n      return;\n    }\n    datepicker.setDate(currentDate, {render: false});\n    picker.update();\n  }\n  if (picker.viewDate !== currentDate) {\n    picker.changeFocus(currentDate);\n  }\n  picker.changeView(0).render();\n}\n\nexport function onClickClearBtn(datepicker) {\n  datepicker.setDate({clear: true});\n}\n\nexport function onClickViewSwitch(datepicker) {\n  switchView(datepicker);\n}\n\nexport function onClickPrevBtn(datepicker) {\n  goToPrevOrNext(datepicker, -1);\n}\n\nexport function onClickNextBtn(datepicker) {\n  goToPrevOrNext(datepicker, 1);\n}\n\n// For the picker's main block to delegete the events from `datepicker-cell`s\nexport function onClickView(datepicker, ev) {\n  const target = findElementInEventPath(ev, '.datepicker-cell');\n  if (!target || target.classList.contains('disabled')) {\n    return;\n  }\n\n  const {id, isMinView} = datepicker.picker.currentView;\n  if (isMinView) {\n    datepicker.setDate(Number(target.dataset.date));\n  } else if (id === 1) {\n    goToSelectedMonthOrYear(datepicker, Number(target.dataset.month));\n  } else {\n    goToSelectedMonthOrYear(datepicker, Number(target.dataset.year));\n  }\n}\n\nexport function onClickPicker(datepicker) {\n  if (!datepicker.inline && !datepicker.config.disableTouchKeyboard) {\n    datepicker.inputField.focus();\n  }\n}\n", "import {hasProperty, lastItemOf, isInRange, limitToRange} from '../lib/utils.js';\nimport {today} from '../lib/date.js';\nimport {parseHTML, showElement, hideElement, emptyChildNodes} from '../lib/dom.js';\nimport {registerListeners} from '../lib/event.js';\nimport pickerTemplate from './templates/pickerTemplate.js';\nimport DaysView from './views/DaysView.js';\nimport MonthsView from './views/MonthsView.js';\nimport YearsView from './views/YearsView.js';\nimport {triggerDatepickerEvent} from '../events/functions.js';\nimport {\n  onClickTodayBtn,\n  onClickClearBtn,\n  onClickViewSwitch,\n  onClickPrevBtn,\n  onClickNextBtn,\n  onClickView,\n  onClickPicker,\n} from '../events/pickerListeners.js';\n\nfunction processPickerOptions(picker, options) {\n  if (options.title !== undefined) {\n    if (options.title) {\n      picker.controls.title.textContent = options.title;\n      showElement(picker.controls.title);\n    } else {\n      picker.controls.title.textContent = '';\n      hideElement(picker.controls.title);\n    }\n  }\n  if (options.prevArrow) {\n    const prevBtn = picker.controls.prevBtn;\n    emptyChildNodes(prevBtn);\n    options.prevArrow.forEach((node) => {\n      prevBtn.appendChild(node.cloneNode(true));\n    });\n  }\n  if (options.nextArrow) {\n    const nextBtn = picker.controls.nextBtn;\n    emptyChildNodes(nextBtn);\n    options.nextArrow.forEach((node) => {\n      nextBtn.appendChild(node.cloneNode(true));\n    });\n  }\n  if (options.locale) {\n    picker.controls.todayBtn.textContent = options.locale.today;\n    picker.controls.clearBtn.textContent = options.locale.clear;\n  }\n  if (options.todayBtn !== undefined) {\n    if (options.todayBtn) {\n      showElement(picker.controls.todayBtn);\n    } else {\n      hideElement(picker.controls.todayBtn);\n    }\n  }\n  if (hasProperty(options, 'minDate') || hasProperty(options, 'maxDate')) {\n    const {minDate, maxDate} = picker.datepicker.config;\n    picker.controls.todayBtn.disabled = !isInRange(today(), minDate, maxDate);\n  }\n  if (options.clearBtn !== undefined) {\n    if (options.clearBtn) {\n      showElement(picker.controls.clearBtn);\n    } else {\n      hideElement(picker.controls.clearBtn);\n    }\n  }\n}\n\n// Compute view date to reset, which will be...\n// - the last item of the selected dates or defaultViewDate if no selection\n// - limitted to minDate or maxDate if it exceeds the range\nfunction computeResetViewDate(datepicker) {\n  const {dates, config} = datepicker;\n  const viewDate = dates.length > 0 ? lastItemOf(dates) : config.defaultViewDate;\n  return limitToRange(viewDate, config.minDate, config.maxDate);\n}\n\n// Change current view's view date\nfunction setViewDate(picker, newDate) {\n  const oldViewDate = new Date(picker.viewDate);\n  const newViewDate = new Date(newDate);\n  const {id, year, first, last} = picker.currentView;\n  const viewYear = newViewDate.getFullYear();\n\n  picker.viewDate = newDate;\n  if (viewYear !== oldViewDate.getFullYear()) {\n    triggerDatepickerEvent(picker.datepicker, 'changeYear');\n  }\n  if (newViewDate.getMonth() !== oldViewDate.getMonth()) {\n    triggerDatepickerEvent(picker.datepicker, 'changeMonth');\n  }\n\n  // return whether the new date is in different period on time from the one\n  // displayed in the current view\n  // when true, the view needs to be re-rendered on the next UI refresh.\n  switch (id) {\n    case 0:\n      return newDate < first || newDate > last;\n    case 1:\n      return viewYear !== year;\n    default:\n      return viewYear < first || viewYear > last;\n  }\n}\n\nfunction getTextDirection(el) {\n  return window.getComputedStyle(el).direction;\n}\n\n// Class representing the picker UI\nexport default class Picker {\n  constructor(datepicker) {\n    this.datepicker = datepicker;\n\n    const template = pickerTemplate.replace(/%buttonClass%/g, datepicker.config.buttonClass);\n    const element = this.element = parseHTML(template).firstChild;\n    const [header, main, footer] = element.firstChild.children;\n    const title = header.firstElementChild;\n    const [prevBtn, viewSwitch, nextBtn] = header.lastElementChild.children;\n    const [todayBtn, clearBtn] = footer.firstChild.children;\n    const controls = {\n      title,\n      prevBtn,\n      viewSwitch,\n      nextBtn,\n      todayBtn,\n      clearBtn,\n    };\n    this.main = main;\n    this.controls = controls;\n\n    const elementClass = datepicker.inline ? 'inline' : 'dropdown';\n    element.classList.add(`datepicker-${elementClass}`);\n    elementClass === 'dropdown' ? element.classList.add('dropdown', 'absolute', 'top-0', 'left-0', 'z-20', 'pt-2') : null;\n\n    processPickerOptions(this, datepicker.config);\n    this.viewDate = computeResetViewDate(datepicker);\n\n    // set up event listeners\n    registerListeners(datepicker, [\n      [element, 'click', onClickPicker.bind(null, datepicker), {capture: true}],\n      [main, 'click', onClickView.bind(null, datepicker)],\n      [controls.viewSwitch, 'click', onClickViewSwitch.bind(null, datepicker)],\n      [controls.prevBtn, 'click', onClickPrevBtn.bind(null, datepicker)],\n      [controls.nextBtn, 'click', onClickNextBtn.bind(null, datepicker)],\n      [controls.todayBtn, 'click', onClickTodayBtn.bind(null, datepicker)],\n      [controls.clearBtn, 'click', onClickClearBtn.bind(null, datepicker)],\n    ]);\n\n    // set up views\n    this.views = [\n      new DaysView(this),\n      new MonthsView(this),\n      new YearsView(this, {id: 2, name: 'years', cellClass: 'year', step: 1}),\n      new YearsView(this, {id: 3, name: 'decades', cellClass: 'decade', step: 10}),\n    ];\n    this.currentView = this.views[datepicker.config.startView];\n\n    this.currentView.render();\n    this.main.appendChild(this.currentView.element);\n    datepicker.config.container.appendChild(this.element);\n  }\n\n  setOptions(options) {\n    processPickerOptions(this, options);\n    this.views.forEach((view) => {\n      view.init(options, false);\n    });\n    this.currentView.render();\n  }\n\n  detach() {\n    this.datepicker.config.container.removeChild(this.element);\n  }\n\n  show() {\n    if (this.active) {\n      return;\n    }\n    this.element.classList.add('active', 'block');\n    this.element.classList.remove('hidden');\n    this.active = true;\n\n    const datepicker = this.datepicker;\n    if (!datepicker.inline) {\n      // ensure picker's direction matches input's\n      const inputDirection = getTextDirection(datepicker.inputField);\n      if (inputDirection !== getTextDirection(datepicker.config.container)) {\n        this.element.dir = inputDirection;\n      } else if (this.element.dir) {\n        this.element.removeAttribute('dir');\n      }\n\n      this.place();\n      if (datepicker.config.disableTouchKeyboard) {\n        datepicker.inputField.blur();\n      }\n    }\n    triggerDatepickerEvent(datepicker, 'show');\n  }\n\n  hide() {\n    if (!this.active) {\n      return;\n    }\n    this.datepicker.exitEditMode();\n    this.element.classList.remove('active', 'block');\n    this.element.classList.add('active', 'block', 'hidden');\n    this.active = false;\n    triggerDatepickerEvent(this.datepicker, 'hide');\n  }\n\n  place() {\n    const {classList, style} = this.element;\n    const {config, inputField} = this.datepicker;\n    const container = config.container;\n    const {\n      width: calendarWidth,\n      height: calendarHeight,\n    } = this.element.getBoundingClientRect();\n    const {\n      left: containerLeft,\n      top: containerTop,\n      width: containerWidth,\n    } = container.getBoundingClientRect();\n    const {\n      left: inputLeft,\n      top: inputTop,\n      width: inputWidth,\n      height: inputHeight\n    } = inputField.getBoundingClientRect();\n    let {x: orientX, y: orientY} = config.orientation;\n    let scrollTop;\n    let left;\n    let top;\n\n    if (container === document.body) {\n      scrollTop = window.scrollY;\n      left = inputLeft + window.scrollX;\n      top = inputTop + scrollTop;\n    } else {\n      scrollTop = container.scrollTop;\n      left = inputLeft - containerLeft;\n      top = inputTop - containerTop + scrollTop;\n    }\n\n    if (orientX === 'auto') {\n      if (left < 0) {\n        // align to the left and move into visible area if input's left edge < window's\n        orientX = 'left';\n        left = 10;\n      } else if (left + calendarWidth > containerWidth) {\n        // align to the right if canlendar's right edge > container's\n        orientX = 'right';\n      } else {\n        orientX = getTextDirection(inputField) === 'rtl' ? 'right' : 'left';\n      }\n    }\n    if (orientX === 'right') {\n      left -= calendarWidth - inputWidth;\n    }\n\n    if (orientY === 'auto') {\n      orientY = top - calendarHeight < scrollTop ? 'bottom' : 'top';\n    }\n    if (orientY === 'top') {\n      top -= calendarHeight;\n    } else {\n      top += inputHeight;\n    }\n\n    classList.remove(\n      'datepicker-orient-top',\n      'datepicker-orient-bottom',\n      'datepicker-orient-right',\n      'datepicker-orient-left'\n    );\n    classList.add(`datepicker-orient-${orientY}`, `datepicker-orient-${orientX}`);\n\n    style.top = top ? `${top}px` : top;\n    style.left = left ? `${left}px` : left;\n  }\n\n  setViewSwitchLabel(labelText) {\n    this.controls.viewSwitch.textContent = labelText;\n  }\n\n  setPrevBtnDisabled(disabled) {\n    this.controls.prevBtn.disabled = disabled;\n  }\n\n  setNextBtnDisabled(disabled) {\n    this.controls.nextBtn.disabled = disabled;\n  }\n\n  changeView(viewId) {\n    const oldView = this.currentView;\n    const newView =  this.views[viewId];\n    if (newView.id !== oldView.id) {\n      this.currentView = newView;\n      this._renderMethod = 'render';\n      triggerDatepickerEvent(this.datepicker, 'changeView');\n      this.main.replaceChild(newView.element, oldView.element);\n    }\n    return this;\n  }\n\n  // Change the focused date (view date)\n  changeFocus(newViewDate) {\n    this._renderMethod = setViewDate(this, newViewDate) ? 'render' : 'refreshFocus';\n    this.views.forEach((view) => {\n      view.updateFocus();\n    });\n    return this;\n  }\n\n  // Apply the change of the selected dates\n  update() {\n    const newViewDate = computeResetViewDate(this.datepicker);\n    this._renderMethod = setViewDate(this, newViewDate) ? 'render' : 'refresh';\n    this.views.forEach((view) => {\n      view.updateFocus();\n      view.updateSelection();\n    });\n    return this;\n  }\n\n  // Refresh the picker UI\n  render(quickRender = true) {\n    const renderMethod = (quickRender && this._renderMethod) || 'render';\n    delete this._renderMethod;\n\n    this.currentView[renderMethod]();\n  }\n}\n", "import {isInRange} from '../lib/utils.js';\nimport {addDays, addMonths, addYears, startOfYearPeriod} from '../lib/date.js';\nimport {goToPrevOrNext, switchView, unfocus} from './functions.js';\n\n// Find the closest date that doesn't meet the condition for unavailable date\n// Returns undefined if no available date is found\n// addFn: function to calculate the next date\n//   - args: time value, amount\n// increase: amount to pass to addFn\n// testFn: function to test the unavailablity of the date\n//   - args: time value; retun: true if unavailable\nfunction findNextAvailableOne(date, addFn, increase, testFn, min, max) {\n  if (!isInRange(date, min, max)) {\n    return;\n  }\n  if (testFn(date)) {\n    const newDate = addFn(date, increase);\n    return findNextAvailableOne(newDate, addFn, increase, testFn, min, max);\n  }\n  return date;\n}\n\n// direction: -1 (left/up), 1 (right/down)\n// vertical: true for up/down, false for left/right\nfunction moveByArrowKey(datepicker, ev, direction, vertical) {\n  const picker = datepicker.picker;\n  const currentView = picker.currentView;\n  const step = currentView.step || 1;\n  let viewDate = picker.viewDate;\n  let addFn;\n  let testFn;\n  switch (currentView.id) {\n    case 0:\n      if (vertical) {\n        viewDate = addDays(viewDate, direction * 7);\n      } else if (ev.ctrlKey || ev.metaKey) {\n        viewDate = addYears(viewDate, direction);\n      } else {\n        viewDate = addDays(viewDate, direction);\n      }\n      addFn = addDays;\n      testFn = (date) => currentView.disabled.includes(date);\n      break;\n    case 1:\n      viewDate = addMonths(viewDate, vertical ? direction * 4 : direction);\n      addFn = addMonths;\n      testFn = (date) => {\n        const dt = new Date(date);\n        const {year, disabled} = currentView;\n        return dt.getFullYear() === year && disabled.includes(dt.getMonth());\n      };\n      break;\n    default:\n      viewDate = addYears(viewDate, direction * (vertical ? 4 : 1) * step);\n      addFn = addYears;\n      testFn = date => currentView.disabled.includes(startOfYearPeriod(date, step));\n  }\n  viewDate = findNextAvailableOne(\n    viewDate,\n    addFn,\n    direction < 0 ? -step : step,\n    testFn,\n    currentView.minDate,\n    currentView.maxDate\n  );\n  if (viewDate !== undefined) {\n    picker.changeFocus(viewDate).render();\n  }\n}\n\nexport function onKeydown(datepicker, ev) {\n  if (ev.key === 'Tab') {\n    unfocus(datepicker);\n    return;\n  }\n\n  const picker = datepicker.picker;\n  const {id, isMinView} = picker.currentView;\n  if (!picker.active) {\n    switch (ev.key) {\n      case 'ArrowDown':\n      case 'Escape':\n        picker.show();\n        break;\n      case 'Enter':\n        datepicker.update();\n        break;\n      default:\n        return;\n    }\n  } else if (datepicker.editMode) {\n    switch (ev.key) {\n      case 'Escape':\n        picker.hide();\n        break;\n      case 'Enter':\n        datepicker.exitEditMode({update: true, autohide: datepicker.config.autohide});\n        break;\n      default:\n        return;\n    }\n  } else {\n    switch (ev.key) {\n      case 'Escape':\n        picker.hide();\n        break;\n      case 'ArrowLeft':\n        if (ev.ctrlKey || ev.metaKey) {\n          goToPrevOrNext(datepicker, -1);\n        } else if (ev.shiftKey) {\n          datepicker.enterEditMode();\n          return;\n        } else {\n          moveByArrowKey(datepicker, ev, -1, false);\n        }\n        break;\n      case 'ArrowRight':\n        if (ev.ctrlKey || ev.metaKey) {\n          goToPrevOrNext(datepicker, 1);\n        } else if (ev.shiftKey) {\n          datepicker.enterEditMode();\n          return;\n        } else {\n          moveByArrowKey(datepicker, ev, 1, false);\n        }\n        break;\n      case 'ArrowUp':\n        if (ev.ctrlKey || ev.metaKey) {\n          switchView(datepicker);\n        } else if (ev.shiftKey) {\n          datepicker.enterEditMode();\n          return;\n        } else {\n          moveByArrowKey(datepicker, ev, -1, true);\n        }\n        break;\n      case 'ArrowDown':\n        if (ev.shiftKey && !ev.ctrlKey && !ev.metaKey) {\n          datepicker.enterEditMode();\n          return;\n        }\n        moveByArrowKey(datepicker, ev, 1, true);\n        break;\n      case 'Enter':\n        if (isMinView) {\n          datepicker.setDate(picker.viewDate);\n        } else {\n          picker.changeView(id - 1).render();\n        }\n        break;\n      case 'Backspace':\n      case 'Delete':\n        datepicker.enterEditMode();\n        return;\n      default:\n        if (ev.key.length === 1 && !ev.ctrlKey && !ev.metaKey) {\n          datepicker.enterEditMode();\n        }\n        return;\n    }\n  }\n  ev.preventDefault();\n  ev.stopPropagation();\n}\n\nexport function onFocus(datepicker) {\n  if (datepicker.config.showOnFocus && !datepicker._showing) {\n    datepicker.show();\n  }\n}\n\n// for the prevention for entering edit mode while getting focus on click\nexport function onMousedown(datepicker, ev) {\n  const el = ev.target;\n  if (datepicker.picker.active || datepicker.config.showOnClick) {\n    el._active = el === document.activeElement;\n    el._clicking = setTimeout(() => {\n      delete el._active;\n      delete el._clicking;\n    }, 2000);\n  }\n}\n\nexport function onClickInput(datepicker, ev) {\n  const el = ev.target;\n  if (!el._clicking) {\n    return;\n  }\n  clearTimeout(el._clicking);\n  delete el._clicking;\n\n  if (el._active) {\n    datepicker.enterEditMode();\n  }\n  delete el._active;\n\n  if (datepicker.config.showOnClick) {\n    datepicker.show();\n  }\n}\n\nexport function onPaste(datepicker, ev) {\n  if (ev.clipboardData.types.includes('text/plain')) {\n    datepicker.enterEditMode();\n  }\n}\n", "import {findElementInEventPath} from '../lib/event.js';\nimport {unfocus} from './functions.js';\n\n// for the `document` to delegate the events from outside the picker/input field\nexport function onClickOutside(datepicker, ev) {\n  const element = datepicker.element;\n  if (element !== document.activeElement) {\n    return;\n  }\n  const pickerElem = datepicker.picker.element;\n  if (findElementInEventPath(ev, el => el === element || el === pickerElem)) {\n    return;\n  }\n  unfocus(datepicker);\n}\n", "import {lastItemOf, stringTo<PERSON>rray, isInRange} from './lib/utils.js';\nimport {today} from './lib/date.js';\nimport {parseDate, formatDate} from './lib/date-format.js';\nimport {registerListeners, unregisterListeners} from './lib/event.js';\nimport {locales} from './i18n/base-locales.js';\nimport defaultOptions from './options/defaultOptions.js';\nimport processOptions from './options/processOptions.js';\nimport Picker from './picker/Picker.js';\nimport {triggerDatepickerEvent} from './events/functions.js';\nimport {onKeydown, onFocus, onMousedown, onClickInput, onPaste} from './events/inputFieldListeners.js';\nimport {onClickOutside} from './events/otherListeners.js';\n\nfunction stringifyDates(dates, config) {\n  return dates\n    .map(dt => formatDate(dt, config.format, config.locale))\n    .join(config.dateDelimiter);\n}\n\n// parse input dates and create an array of time values for selection\n// returns undefined if there are no valid dates in inputDates\n// when origDates (current selection) is passed, the function works to mix\n// the input dates into the current selection\nfunction processInputDates(datepicker, inputDates, clear = false) {\n  const {config, dates: origDates, rangepicker} = datepicker;\n  if (inputDates.length === 0) {\n    // empty input is considered valid unless origiDates is passed\n    return clear ? [] : undefined;\n  }\n\n  const rangeEnd = rangepicker && datepicker === rangepicker.datepickers[1];\n  let newDates = inputDates.reduce((dates, dt) => {\n    let date = parseDate(dt, config.format, config.locale);\n    if (date === undefined) {\n      return dates;\n    }\n    if (config.pickLevel > 0) {\n      // adjust to 1st of the month/Jan 1st of the year\n      // or to the last day of the monh/Dec 31st of the year if the datepicker\n      // is the range-end picker of a rangepicker\n      const dt = new Date(date);\n      if (config.pickLevel === 1) {\n        date = rangeEnd\n          ? dt.setMonth(dt.getMonth() + 1, 0)\n          : dt.setDate(1);\n      } else {\n        date = rangeEnd\n          ? dt.setFullYear(dt.getFullYear() + 1, 0, 0)\n          : dt.setMonth(0, 1);\n      }\n    }\n    if (\n      isInRange(date, config.minDate, config.maxDate)\n      && !dates.includes(date)\n      && !config.datesDisabled.includes(date)\n      && !config.daysOfWeekDisabled.includes(new Date(date).getDay())\n    ) {\n      dates.push(date);\n    }\n    return dates;\n  }, []);\n  if (newDates.length === 0) {\n    return;\n  }\n  if (config.multidate && !clear) {\n    // get the synmetric difference between origDates and newDates\n    newDates = newDates.reduce((dates, date) => {\n      if (!origDates.includes(date)) {\n        dates.push(date);\n      }\n      return dates;\n    }, origDates.filter(date => !newDates.includes(date)));\n  }\n  // do length check always because user can input multiple dates regardless of the mode\n  return config.maxNumberOfDates && newDates.length > config.maxNumberOfDates\n    ? newDates.slice(config.maxNumberOfDates * -1)\n    : newDates;\n}\n\n// refresh the UI elements\n// modes: 1: input only, 2, picker only, 3 both\nfunction refreshUI(datepicker, mode = 3, quickRender = true) {\n  const {config, picker, inputField} = datepicker;\n  if (mode & 2) {\n    const newView = picker.active ? config.pickLevel : config.startView;\n    picker.update().changeView(newView).render(quickRender);\n  }\n  if (mode & 1 && inputField) {\n    inputField.value = stringifyDates(datepicker.dates, config);\n  }\n}\n\nfunction setDate(datepicker, inputDates, options) {\n  let {clear, render, autohide} = options;\n  if (render === undefined) {\n    render = true;\n  }\n  if (!render) {\n    autohide = false;\n  } else if (autohide === undefined) {\n    autohide = datepicker.config.autohide;\n  }\n\n  const newDates = processInputDates(datepicker, inputDates, clear);\n  if (!newDates) {\n    return;\n  }\n  if (newDates.toString() !== datepicker.dates.toString()) {\n    datepicker.dates = newDates;\n    refreshUI(datepicker, render ? 3 : 1);\n    triggerDatepickerEvent(datepicker, 'changeDate');\n  } else {\n    refreshUI(datepicker, 1);\n  }\n  if (autohide) {\n    datepicker.hide();\n  }\n}\n\n/**\n * Class representing a date picker\n */\nexport default class Datepicker {\n  /**\n   * Create a date picker\n   * @param  {Element} element - element to bind a date picker\n   * @param  {Object} [options] - config options\n   * @param  {DateRangePicker} [rangepicker] - DateRangePicker instance the\n   * date picker belongs to. Use this only when creating date picker as a part\n   * of date range picker\n   */\n  constructor(element, options = {}, rangepicker = undefined) {\n    element.datepicker = this;\n    this.element = element;\n\n    // set up config\n    const config = this.config = Object.assign({\n      buttonClass: (options.buttonClass && String(options.buttonClass)) || 'button',\n      container: document.body,\n      defaultViewDate: today(),\n      maxDate: undefined,\n      minDate: undefined,\n    }, processOptions(defaultOptions, this));\n    this._options = options;\n    Object.assign(config, processOptions(options, this));\n\n    // configure by type\n    const inline = this.inline = element.tagName !== 'INPUT';\n    let inputField;\n    let initialDates;\n\n    if (inline) {\n      config.container = element;\n      initialDates = stringToArray(element.dataset.date, config.dateDelimiter);\n      delete element.dataset.date;\n    } else {\n      const container = options.container ? document.querySelector(options.container) : null;\n      if (container) {\n        config.container = container;\n      }\n      inputField = this.inputField = element;\n      inputField.classList.add('datepicker-input');\n      initialDates = stringToArray(inputField.value, config.dateDelimiter);\n    }\n    if (rangepicker) {\n      // check validiry\n      const index = rangepicker.inputs.indexOf(inputField);\n      const datepickers = rangepicker.datepickers;\n      if (index < 0 || index > 1 || !Array.isArray(datepickers)) {\n        throw Error('Invalid rangepicker object.');\n      }\n      // attach itaelf to the rangepicker here so that processInputDates() can\n      // determine if this is the range-end picker of the rangepicker while\n      // setting inital values when pickLevel > 0\n      datepickers[index] = this;\n      // add getter for rangepicker\n      Object.defineProperty(this, 'rangepicker', {\n        get() {\n          return rangepicker;\n        },\n      });\n    }\n\n    // set initial dates\n    this.dates = [];\n    // process initial value\n    const inputDateValues = processInputDates(this, initialDates);\n    if (inputDateValues && inputDateValues.length > 0) {\n      this.dates = inputDateValues;\n    }\n    if (inputField) {\n      inputField.value = stringifyDates(this.dates, config);\n    }\n\n    const picker = this.picker = new Picker(this);\n\n    if (inline) {\n      this.show();\n    } else {\n      // set up event listeners in other modes\n      const onMousedownDocument = onClickOutside.bind(null, this);\n      const listeners = [\n        [inputField, 'keydown', onKeydown.bind(null, this)],\n        [inputField, 'focus', onFocus.bind(null, this)],\n        [inputField, 'mousedown', onMousedown.bind(null, this)],\n        [inputField, 'click', onClickInput.bind(null, this)],\n        [inputField, 'paste', onPaste.bind(null, this)],\n        [document, 'mousedown', onMousedownDocument],\n        [document, 'touchstart', onMousedownDocument],\n        [window, 'resize', picker.place.bind(picker)]\n      ];\n      registerListeners(this, listeners);\n    }\n  }\n\n  /**\n   * Format Date object or time value in given format and language\n   * @param  {Date|Number} date - date or time value to format\n   * @param  {String|Object} format - format string or object that contains\n   * toDisplay() custom formatter, whose signature is\n   * - args:\n   *   - date: {Date} - Date instance of the date passed to the method\n   *   - format: {Object} - the format object passed to the method\n   *   - locale: {Object} - locale for the language specified by `lang`\n   * - return:\n   *     {String} formatted date\n   * @param  {String} [lang=en] - language code for the locale to use\n   * @return {String} formatted date\n   */\n  static formatDate(date, format, lang) {\n    return formatDate(date, format, lang && locales[lang] || locales.en);\n  }\n\n  /**\n   * Parse date string\n   * @param  {String|Date|Number} dateStr - date string, Date object or time\n   * value to parse\n   * @param  {String|Object} format - format string or object that contains\n   * toValue() custom parser, whose signature is\n   * - args:\n   *   - dateStr: {String|Date|Number} - the dateStr passed to the method\n   *   - format: {Object} - the format object passed to the method\n   *   - locale: {Object} - locale for the language specified by `lang`\n   * - return:\n   *     {Date|Number} parsed date or its time value\n   * @param  {String} [lang=en] - language code for the locale to use\n   * @return {Number} time value of parsed date\n   */\n  static parseDate(dateStr, format, lang) {\n    return parseDate(dateStr, format, lang && locales[lang] || locales.en);\n  }\n\n  /**\n   * @type {Object} - Installed locales in `[languageCode]: localeObject` format\n   * en`:_English (US)_ is pre-installed.\n   */\n  static get locales() {\n    return locales;\n  }\n\n  /**\n   * @type {Boolean} - Whether the picker element is shown. `true` whne shown\n   */\n  get active() {\n    return !!(this.picker && this.picker.active);\n  }\n\n  /**\n   * @type {HTMLDivElement} - DOM object of picker element\n   */\n  get pickerElement() {\n    return this.picker ? this.picker.element : undefined;\n  }\n\n  /**\n   * Set new values to the config options\n   * @param {Object} options - config options to update\n   */\n  setOptions(options) {\n    const picker = this.picker;\n    const newOptions = processOptions(options, this);\n    Object.assign(this._options, options);\n    Object.assign(this.config, newOptions);\n    picker.setOptions(newOptions);\n\n    refreshUI(this, 3);\n  }\n\n  /**\n   * Show the picker element\n   */\n  show() {\n    if (this.inputField) {\n      if (this.inputField.disabled) {\n        return;\n      }\n      if (this.inputField !== document.activeElement) {\n        this._showing = true;\n        this.inputField.focus();\n        delete this._showing;\n      }\n    }\n    this.picker.show();\n  }\n\n  /**\n   * Hide the picker element\n   * Not available on inline picker\n   */\n  hide() {\n    if (this.inline) {\n      return;\n    }\n    this.picker.hide();\n    this.picker.update().changeView(this.config.startView).render();\n  }\n\n  /**\n   * Destroy the Datepicker instance\n   * @return {Detepicker} - the instance destroyed\n   */\n  destroy() {\n    this.hide();\n    unregisterListeners(this);\n    this.picker.detach();\n    if (!this.inline) {\n      this.inputField.classList.remove('datepicker-input');\n    }\n    delete this.element.datepicker;\n    return this;\n  }\n\n  /**\n   * Get the selected date(s)\n   *\n   * The method returns a Date object of selected date by default, and returns\n   * an array of selected dates in multidate mode. If format string is passed,\n   * it returns date string(s) formatted in given format.\n   *\n   * @param  {String} [format] - Format string to stringify the date(s)\n   * @return {Date|String|Date[]|String[]} - selected date(s), or if none is\n   * selected, empty array in multidate mode and untitled in sigledate mode\n   */\n  getDate(format = undefined) {\n    const callback = format\n      ? date => formatDate(date, format, this.config.locale)\n      : date => new Date(date);\n\n    if (this.config.multidate) {\n      return this.dates.map(callback);\n    }\n    if (this.dates.length > 0) {\n      return callback(this.dates[0]);\n    }\n  }\n\n  /**\n   * Set selected date(s)\n   *\n   * In multidate mode, you can pass multiple dates as a series of arguments\n   * or an array. (Since each date is parsed individually, the type of the\n   * dates doesn't have to be the same.)\n   * The given dates are used to toggle the select status of each date. The\n   * number of selected dates is kept from exceeding the length set to\n   * maxNumberOfDates.\n   *\n   * With clear: true option, the method can be used to clear the selection\n   * and to replace the selection instead of toggling in multidate mode.\n   * If the option is passed with no date arguments or an empty dates array,\n   * it works as \"clear\" (clear the selection then set nothing), and if the\n   * option is passed with new dates to select, it works as \"replace\" (clear\n   * the selection then set the given dates)\n   *\n   * When render: false option is used, the method omits re-rendering the\n   * picker element. In this case, you need to call refresh() method later in\n   * order for the picker element to reflect the changes. The input field is\n   * refreshed always regardless of this option.\n   *\n   * When invalid (unparsable, repeated, disabled or out-of-range) dates are\n   * passed, the method ignores them and applies only valid ones. In the case\n   * that all the given dates are invalid, which is distinguished from passing\n   * no dates, the method considers it as an error and leaves the selection\n   * untouched.\n   *\n   * @param {...(Date|Number|String)|Array} [dates] - Date strings, Date\n   * objects, time values or mix of those for new selection\n   * @param {Object} [options] - function options\n   * - clear: {boolean} - Whether to clear the existing selection\n   *     defualt: false\n   * - render: {boolean} - Whether to re-render the picker element\n   *     default: true\n   * - autohide: {boolean} - Whether to hide the picker element after re-render\n   *     Ignored when used with render: false\n   *     default: config.autohide\n   */\n  setDate(...args) {\n    const dates = [...args];\n    const opts = {};\n    const lastArg = lastItemOf(args);\n    if (\n      typeof lastArg === 'object'\n      && !Array.isArray(lastArg)\n      && !(lastArg instanceof Date)\n      && lastArg\n    ) {\n      Object.assign(opts, dates.pop());\n    }\n\n    const inputDates = Array.isArray(dates[0]) ? dates[0] : dates;\n    setDate(this, inputDates, opts);\n  }\n\n  /**\n   * Update the selected date(s) with input field's value\n   * Not available on inline picker\n   *\n   * The input field will be refreshed with properly formatted date string.\n   *\n   * @param  {Object} [options] - function options\n   * - autohide: {boolean} - whether to hide the picker element after refresh\n   *     default: false\n   */\n  update(options = undefined) {\n    if (this.inline) {\n      return;\n    }\n\n    const opts = {clear: true, autohide: !!(options && options.autohide)};\n    const inputDates = stringToArray(this.inputField.value, this.config.dateDelimiter);\n    setDate(this, inputDates, opts);\n  }\n\n  /**\n   * Refresh the picker element and the associated input field\n   * @param {String} [target] - target item when refreshing one item only\n   * 'picker' or 'input'\n   * @param {Boolean} [forceRender] - whether to re-render the picker element\n   * regardless of its state instead of optimized refresh\n   */\n  refresh(target = undefined, forceRender = false) {\n    if (target && typeof target !== 'string') {\n      forceRender = target;\n      target = undefined;\n    }\n\n    let mode;\n    if (target === 'picker') {\n      mode = 2;\n    } else if (target === 'input') {\n      mode = 1;\n    } else {\n      mode = 3;\n    }\n    refreshUI(this, mode, !forceRender);\n  }\n\n  /**\n   * Enter edit mode\n   * Not available on inline picker or when the picker element is hidden\n   */\n  enterEditMode() {\n    if (this.inline || !this.picker.active || this.editMode) {\n      return;\n    }\n    this.editMode = true;\n    this.inputField.classList.add('in-edit', 'border-blue-700', '!border-primary-700');\n  }\n\n  /**\n   * Exit from edit mode\n   * Not available on inline picker\n   * @param  {Object} [options] - function options\n   * - update: {boolean} - whether to call update() after exiting\n   *     If false, input field is revert to the existing selection\n   *     default: false\n   */\n  exitEditMode(options = undefined) {\n    if (this.inline || !this.editMode) {\n      return;\n    }\n    const opts = Object.assign({update: false}, options);\n    delete this.editMode;\n    this.inputField.classList.remove('in-edit', 'border-blue-700', '!border-primary-700');\n    if (opts.update) {\n      this.update(opts);\n    }\n  }\n}\n", "import {stripTime, today} from './date.js';\nimport {lastItemOf} from './utils.js';\n\n// pattern for format parts\nexport const reFormatTokens = /dd?|DD?|mm?|MM?|yy?(?:yy)?/;\n// pattern for non date parts\nexport const reNonDateParts = /[\\s!-/:-@[-`{-~年月日]+/;\n// cache for persed formats\nlet knownFormats = {};\n// parse funtions for date parts\nconst parseFns = {\n  y(date, year) {\n    return new Date(date).setFullYear(parseInt(year, 10));\n  },\n  m(date, month, locale) {\n    const newDate = new Date(date);\n    let monthIndex = parseInt(month, 10) - 1;\n\n    if (isNaN(monthIndex)) {\n      if (!month) {\n        return NaN;\n      }\n\n      const monthName = month.toLowerCase();\n      const compareNames = name => name.toLowerCase().startsWith(monthName);\n      // compare with both short and full names because some locales have periods\n      // in the short names (not equal to the first X letters of the full names)\n      monthIndex = locale.monthsShort.findIndex(compareNames);\n      if (monthIndex < 0) {\n        monthIndex = locale.months.findIndex(compareNames);\n      }\n      if (monthIndex < 0) {\n        return NaN;\n      }\n    }\n\n    newDate.setMonth(monthIndex);\n    return newDate.getMonth() !== normalizeMonth(monthIndex)\n      ? newDate.setDate(0)\n      : newDate.getTime();\n  },\n  d(date, day) {\n    return new Date(date).setDate(parseInt(day, 10));\n  },\n};\n// format functions for date parts\nconst formatFns = {\n  d(date) {\n    return date.getDate();\n  },\n  dd(date) {\n    return padZero(date.getDate(), 2);\n  },\n  D(date, locale) {\n    return locale.daysShort[date.getDay()];\n  },\n  DD(date, locale) {\n    return locale.days[date.getDay()];\n  },\n  m(date) {\n    return date.getMonth() + 1;\n  },\n  mm(date) {\n    return padZero(date.getMonth() + 1, 2);\n  },\n  M(date, locale) {\n    return locale.monthsShort[date.getMonth()];\n  },\n  MM(date, locale) {\n    return locale.months[date.getMonth()];\n  },\n  y(date) {\n    return date.getFullYear();\n  },\n  yy(date) {\n    return padZero(date.getFullYear(), 2).slice(-2);\n  },\n  yyyy(date) {\n    return padZero(date.getFullYear(), 4);\n  },\n};\n\n// get month index in normal range (0 - 11) from any number\nfunction normalizeMonth(monthIndex) {\n  return monthIndex > -1 ? monthIndex % 12 : normalizeMonth(monthIndex + 12);\n}\n\nfunction padZero(num, length) {\n  return num.toString().padStart(length, '0');\n}\n\nfunction parseFormatString(format) {\n  if (typeof format !== 'string') {\n    throw new Error(\"Invalid date format.\");\n  }\n  if (format in knownFormats) {\n    return knownFormats[format];\n  }\n\n  // sprit the format string into parts and seprators\n  const separators = format.split(reFormatTokens);\n  const parts = format.match(new RegExp(reFormatTokens, 'g'));\n  if (separators.length === 0 || !parts) {\n    throw new Error(\"Invalid date format.\");\n  }\n\n  // collect format functions used in the format\n  const partFormatters = parts.map(token => formatFns[token]);\n\n  // collect parse function keys used in the format\n  // iterate over parseFns' keys in order to keep the order of the keys.\n  const partParserKeys = Object.keys(parseFns).reduce((keys, key) => {\n    const token = parts.find(part => part[0] !== 'D' && part[0].toLowerCase() === key);\n    if (token) {\n      keys.push(key);\n    }\n    return keys;\n  }, []);\n\n  return knownFormats[format] = {\n    parser(dateStr, locale) {\n      const dateParts = dateStr.split(reNonDateParts).reduce((dtParts, part, index) => {\n        if (part.length > 0 && parts[index]) {\n          const token = parts[index][0];\n          if (token === 'M') {\n            dtParts.m = part;\n          } else if (token !== 'D') {\n            dtParts[token] = part;\n          }\n        }\n        return dtParts;\n      }, {});\n\n      // iterate over partParserkeys so that the parsing is made in the oder\n      // of year, month and day to prevent the day parser from correcting last\n      // day of month wrongly\n      return partParserKeys.reduce((origDate, key) => {\n        const newDate = parseFns[key](origDate, dateParts[key], locale);\n        // ingnore the part failed to parse\n        return isNaN(newDate) ? origDate : newDate;\n      }, today());\n    },\n    formatter(date, locale) {\n      let dateStr = partFormatters.reduce((str, fn, index) => {\n        return str += `${separators[index]}${fn(date, locale)}`;\n      }, '');\n      // separators' length is always parts' length + 1,\n      return dateStr += lastItemOf(separators);\n    },\n  };\n}\n\nexport function parseDate(dateStr, format, locale) {\n  if (dateStr instanceof Date || typeof dateStr === 'number') {\n    const date = stripTime(dateStr);\n    return isNaN(date) ? undefined : date;\n  }\n  if (!dateStr) {\n    return undefined;\n  }\n  if (dateStr === 'today') {\n    return today();\n  }\n\n  if (format && format.toValue) {\n    const date = format.toValue(dateStr, format, locale);\n    return isNaN(date) ? undefined : stripTime(date);\n  }\n\n  return parseFormatString(format).parser(dateStr, locale);\n}\n\nexport function formatDate(date, format, locale) {\n  if (isNaN(date) || (!date && date !== 0)) {\n    return '';\n  }\n\n  const dateObj = typeof date === 'number' ? new Date(date) : date;\n\n  if (format.toDisplay) {\n    return format.toDisplay(dateObj, format, locale);\n  }\n\n  return parseFormatString(format).formatter(dateObj, locale);\n}\n", "export function stripTime(timeValue) {\n  return new Date(timeValue).setHours(0, 0, 0, 0);\n}\n\nexport function today() {\n  return new Date().setHours(0, 0, 0, 0);\n}\n\n// Get the time value of the start of given date or year, month and day\nexport function dateValue(...args) {\n  switch (args.length) {\n    case 0:\n      return today();\n    case 1:\n      return stripTime(args[0]);\n  }\n\n  // use setFullYear() to keep 2-digit year from being mapped to 1900-1999\n  const newDate = new Date(0);\n  newDate.setFullYear(...args);\n  return newDate.setHours(0, 0, 0, 0);\n}\n\nexport function addDays(date, amount) {\n  const newDate = new Date(date);\n  return newDate.setDate(newDate.getDate() + amount);\n}\n\nexport function addWeeks(date, amount) {\n  return addDays(date, amount * 7);\n}\n\nexport function addMonths(date, amount) {\n  // If the day of the date is not in the new month, the last day of the new\n  // month will be returned. e.g. Jan 31 + 1 month → Feb 28 (not Mar 03)\n  const newDate = new Date(date);\n  const monthsToSet = newDate.getMonth() + amount;\n  let expectedMonth = monthsToSet % 12;\n  if (expectedMonth < 0) {\n    expectedMonth += 12;\n  }\n\n  const time = newDate.setMonth(monthsToSet);\n  return newDate.getMonth() !== expectedMonth ? newDate.setDate(0) : time;\n}\n\nexport function addYears(date, amount) {\n  // If the date is Feb 29 and the new year is not a leap year, Feb 28 of the\n  // new year will be returned.\n  const newDate = new Date(date);\n  const expectedMonth = newDate.getMonth();\n  const time = newDate.setFullYear(newDate.getFullYear() + amount);\n  return expectedMonth === 1 && newDate.getMonth() === 2 ? newDate.setDate(0) : time;\n}\n\n// Calculate the distance bettwen 2 days of the week\nfunction dayDiff(day, from) {\n  return (day - from + 7) % 7;\n}\n\n// Get the date of the specified day of the week of given base date\nexport function dayOfTheWeekOf(baseDate, dayOfWeek, weekStart = 0) {\n  const baseDay = new Date(baseDate).getDay();\n  return addDays(baseDate, dayDiff(dayOfWeek, weekStart) - dayDiff(baseDay, weekStart));\n}\n\n// Get the ISO week of a date\nexport function getWeek(date) {\n  // start of ISO week is Monday\n  const thuOfTheWeek = dayOfTheWeekOf(date, 4, 1);\n  // 1st week == the week where the 4th of January is in\n  const firstThu = dayOfTheWeekOf(new Date(thuOfTheWeek).setMonth(0, 4), 4, 1);\n  return Math.round((thuOfTheWeek - firstThu) / 604800000) + 1;\n}\n\n// Get the start year of the period of years that includes given date\n// years: length of the year period\nexport function startOfYearPeriod(date, years) {\n  /* @see https://en.wikipedia.org/wiki/Year_zero#ISO_8601 */\n  const year = new Date(date).getFullYear();\n  return Math.floor(year / years) * years;\n}\n", "const listenerRegistry = new WeakMap();\nconst {addEventListener, removeEventListener} = EventTarget.prototype;\n\n// Register event listeners to a key object\n// listeners: array of listener definitions;\n//   - each definition must be a flat array of event target and the arguments\n//     used to call addEventListener() on the target\nexport function registerListeners(keyObj, listeners) {\n  let registered = listenerRegistry.get(keyObj);\n  if (!registered) {\n    registered = [];\n    listenerRegistry.set(keyObj, registered);\n  }\n  listeners.forEach((listener) => {\n    addEventListener.call(...listener);\n    registered.push(listener);\n  });\n}\n\nexport function unregisterListeners(keyObj) {\n  let listeners = listenerRegistry.get(keyObj);\n  if (!listeners) {\n    return;\n  }\n  listeners.forEach((listener) => {\n    removeEventListener.call(...listener);\n  });\n  listenerRegistry.delete(keyObj);\n}\n\n// Event.composedPath() polyfill for Edge\n// based on https://gist.github.com/kleinfreund/e9787d73776c0e3750dcfcdc89f100ec\nif (!Event.prototype.composedPath) {\n  const getComposedPath = (node, path = []) => {\n    path.push(node);\n\n    let parent;\n    if (node.parentNode) {\n      parent = node.parentNode;\n    } else if (node.host) { // ShadowRoot\n      parent = node.host;\n    } else if (node.defaultView) {  // Document\n      parent = node.defaultView;\n    }\n    return parent ? getComposedPath(parent, path) : path;\n  };\n\n  Event.prototype.composedPath = function () {\n    return getComposedPath(this.target);\n  };\n}\n\nfunction findFromPath(path, criteria, currentTarget, index = 0) {\n  const el = path[index];\n  if (criteria(el)) {\n    return el;\n  } else if (el === currentTarget || !el.parentElement) {\n    // stop when reaching currentTarget or <html>\n    return;\n  }\n  return findFromPath(path, criteria, currentTarget, index + 1);\n}\n\n// Search for the actual target of a delegated event\nexport function findElementInEventPath(ev, selector) {\n  const criteria = typeof selector === 'function' ? selector : el => el.matches(selector);\n  return findFromPath(ev.composedPath(), criteria, ev.currentTarget);\n}\n", "export function hasProperty(obj, prop) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}\n\nexport function lastItemOf(arr) {\n  return arr[arr.length - 1];\n}\n\n// push only the items not included in the array\nexport function pushUnique(arr, ...items) {\n  items.forEach((item) => {\n    if (arr.includes(item)) {\n      return;\n    }\n    arr.push(item);\n  });\n  return arr;\n}\n\nexport function stringToArray(str, separator) {\n  // convert empty string to an empty array\n  return str ? str.split(separator) : [];\n}\n\nexport function isInRange(testVal, min, max) {\n  const minOK = min === undefined || testVal >= min;\n  const maxOK = max === undefined || testVal <= max;\n  return minOK && maxOK;\n}\n\nexport function limitToRange(val, min, max) {\n  if (val < min) {\n    return min;\n  }\n  if (val > max) {\n    return max;\n  }\n  return val;\n}\n\nexport function createTagRepeat(tagName, repeat, attributes = {}, index = 0, html = '') {\n  const openTagSrc = Object.keys(attributes).reduce((src, attr) => {\n    let val = attributes[attr];\n    if (typeof val === 'function') {\n      val = val(index);\n    }\n    return `${src} ${attr}=\"${val}\"`;\n  }, tagName);\n  html += `<${openTagSrc}></${tagName}>`;\n\n  const next = index + 1;\n  return next < repeat\n    ? createTagRepeat(tagName, repeat, attributes, next, html)\n    : html;\n}\n\n// Remove the spacing surrounding tags for HTML parser not to create text nodes\n// before/after elements\nexport function optimizeTemplateHTML(html) {\n  return html.replace(/>\\s+/g, '>').replace(/\\s+</, '<');\n}\n", "class Events {\n    private _eventType: string;\n    private _eventFunctions: EventListener[];\n\n    constructor(eventType: string, eventFunctions: EventListener[] = []) {\n        this._eventType = eventType;\n        this._eventFunctions = eventFunctions;\n    }\n\n    init() {\n        this._eventFunctions.forEach((eventFunction) => {\n            if (typeof window !== 'undefined') {\n                window.addEventListener(this._eventType, eventFunction);\n            }\n        });\n    }\n}\n\nexport default Events;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import { initDatepickers } from './datepicker';\nimport Datepicker from 'flowbite-datepicker/Datepicker';\nimport DateRangePicker from 'flowbite-datepicker/DateRangePicker';\nimport Events from '../dom/events';\n\nconst turboLoadEvents = new Events('turbo:load', [initDatepickers]);\nconst turboFrameLoadEvents = new Events('turbo:frame-load', [initDatepickers]);\n\nturboLoadEvents.init();\nturboFrameLoadEvents.init();\n\nexport default {\n    Datepicker,\n    DateRangePicker,\n};\n"], "names": ["Datepicker", "DateRangePicker", "Events", "getDatepickerOptions", "datepickerEl", "buttons", "hasAttribute", "autohide", "format", "orientation", "title", "options", "todayBtn", "clearBtn", "getAttribute", "initDatepickers", "document", "querySelectorAll", "for<PERSON>ach", "events", "init", "turboLoadEvents", "turboFrameLoadEvents"], "sourceRoot": ""}