@tailwind base;
@tailwind components;
@tailwind utilities;


@font-face {
    font-family: 'Poppins';
    src: url('../fonts/Poppins-Regular.ttf') format('truetype'),
    url('../fonts/Poppins-Regular.woff') format('woff'),
    url('../fonts/Poppins-Regular.woff2') format('woff2'),
    url('../fonts/Poppins-Regular.svg') format('svg'),
    url('../fonts/Poppins-Regular.eot') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Poppins-Medium';
    src: url('../fonts/Poppins-Medium.ttf') format('truetype'),
    url('../fonts/Poppins-Medium.woff') format('woff'),
    url('../fonts/Poppins-Medium.woff2') format('woff2'),
    url('../fonts/Poppins-Medium.svg') format('svg'),
    url('../fonts/Poppins-Medium.eot') format('embedded-opentype');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'Poppins-SemiBold';
    src: url('../fonts/Poppins-SemiBold.ttf') format('truetype'),
    url('../fonts/Poppins-SemiBold.woff') format('woff'),
    url('../fonts/Poppins-SemiBold.woff2') format('woff2'),
    url('../fonts/Poppins-SemiBold.svg') format('svg'),
    url('../fonts/Poppins-SemiBold.eot') format('embedded-opentype');
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: 'Poppins-Bold';
    src: url('../fonts/Poppins-Bold.ttf') format('truetype'),
    url('../fonts/Poppins-Bold.woff') format('woff'),
    url('../fonts/Poppins-Bold.woff2') format('woff2'),
    url('../fonts/Poppins-Bold.svg') format('svg'),
    url('../fonts/Poppins-Bold.eot') format('embedded-opentype');
    font-weight: 700;
    font-style: normal;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

.main_login {
    min-height: calc(100vh - 180px);
}

#q3 {
    background: url("/images/select_arrow-down.png") no-repeat;
    background-size: 15px;
    background-position: calc(100% - 0.75rem) center !important;
    -moz-appearance: none !important;
    -webkit-appearance: none !important;
    appearance: none !important;
    padding-right: 2rem !important;

}

#brut_izin_sayisi {
    background: url("/images/arrow_down_grey.png") no-repeat;
    background-position: calc(100% - 0.75rem) center !important;
    -moz-appearance: none !important;
    -webkit-appearance: none !important;
    appearance: none !important;
}


.radio-container input:checked + .custom-radio-label {
    background-color: #0F56AF;
    background-image: url('data:image/svg+xml;utf8,<svg fill="white" height="100%" viewBox="0 0 24 24" width="100%" xmlns="http://www.w3.org/2000/svg"><path d="M9 16.2L4.8 12l-1.4 1.4 5.6 5.6L20.2 7l-1.4-1.4z"/></svg>');
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
}

.sirket_araci_container input:checked + .custom-checkbox-label {
    background-color: #707070;
    background-image: url('data:image/svg+xml;utf8,<svg fill="white" height="100%" viewBox="0 0 24 24" width="100%" xmlns="http://www.w3.org/2000/svg"><path d="M9 16.2L4.8 12l-1.4 1.4 5.6 5.6L20.2 7l-1.4-1.4z"/></svg>');
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
}

#staticModalYanHakSepeti {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
}

#staticModalYanHakSepeti::-webkit-scrollbar {
    display: none;
}


#yan_hak_sepeti {
    -webkit-overflow-scrolling: touch;
}

/* Hide scrollbar in Firefox */
#yan_hak_sepeti {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

/* Hide scrollbar in WebKit (Chrome, Safari) */
#yan_hak_sepeti::-webkit-scrollbar {
    display: none !important; /* WebKit */
}

/* Hide scrollbar in IE and Edge */
#yan_hak_sepeti {
    -ms-overflow-style: none !important; /* IE and Edge */
    scrollbar-width: none !important; /* Firefox */
    overflow-y: scroll;
}


/******************************************************/
.center {
    text-align: center;
}


.kvvk_popup::-webkit-scrollbar {
    display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.kvvk_popup {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
}

.core_benefit_text {
    width: 100%;
    max-width: 300px;
}

.temel_hak_p {
    width: 90%;
}

.slider_title {
    max-width: 370px;
    left: 10%;
}

@media (max-width: 500px) {
    .core_benefit_text {
        max-width: 150px;
    }

    .slider_title {
        max-width: 277px;
        left: 12%;
    }
}

/* HELP PAGE */
.help-desc strong {
    color: #7F35B2
}

.help-desc + p strong {
    color: #7F35B2
}

.help-desc + p {
    font-size: 14px;
}

/* HELP PAGE */

/* DASHBOARD */

#welcomeModal {
    z-index: 999999999999;
}

strong {
    color: #7F35B2 !important;
}


#relativeModal {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}


#frmSecenekli_123 .further_details {
    cursor: pointer;
}

#accordion15 .further_details,
#accordion18 .further_details {
    display: none;
}

/*SLIDER STYLE */

#slider {
    position: relative;
    overflow: hidden;

    background-color: #fff;
    border-radius: 15px;

}

#slides {
    display: flex;
    transition: transform 0.5s ease-in-out;

}

.slide {
    min-width: 100%;
    box-sizing: border-box;
    padding: 2.5rem 1rem;
}

.control_next {
    position: absolute;
    bottom: 0px;
    width: auto;
    margin-top: -25px;
    padding: 16px;
    color: #fff;
    font-size: 30px;
    transition: background-color 0.3s;
    text-decoration: none;
}

.control_next {
    right: 0;
}

/*SLIDER STYLE */

@media (max-width: 768px) {

    #frmSecenekli_123 .mobile_meal_ticket,
    #accordion15 .mobile_meal_ticket,
    #accordion18 .mobile_meal_ticket {
        display: none !important;
    }

    #frmSecenekli_123 .desktop_meal_ticket,
    #accordion15 .desktop_meal_ticket,
    #accordion18 .desktop_meal_ticket {
        display: block !important;
    }

    #frmSecenekli_159 .mobile_meal_ticket {
        display: none !important;
    }

    #frmSecenekli_159 .desktop_meal_ticket {
        display: block !important;
    }
}

/* ******** */

.dark .sirket_araci_container [type=checkbox]:checked,
.dark .sirket_araci_container [type=radio]:checked,
.sirket_araci_container [type=checkbox]:checked,
.sirket_araci_container [type=radio]:checked {
    background-color: #707070;
}

.sirket_araci_container input:checked + .custom-checkbox-label {
    background-color: #707070;
}

.login_parent [type=checkbox]:checked,.login_parent [type=radio]:checked,#okudum_anladim_link [type=radio]:checked,input[name="do_not_show_again"]:checked,
input[name="do_not_show_again_slider"]:checked
{
    background-color: #7F35B2;
}
input[name="do_not_show_again_slider"],input[name="do_not_show_again"],.login_parent [type=checkbox],.login_parent [type=radio]{
    accent-color: #7F35B2;;
}

.negative-balance {
    color: red;
}

.details_inner_div p {
    color: #2F2C31;
    font-size: 13px;
}

.details_inner_div::-webkit-scrollbar {
    display: none;
}

.details_inner_div {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.negative-balance-btn {
    background-color: red;
}

.triangle-container {
    display: none;
    position: absolute;
    right: -230px;
    top: -1rem;
    z-index: 50;
    background-color: #fff;
    /* Red color */
    width: 195px;
    border-radius: 8px;
    color: #7F35B2;
    -webkit-box-shadow: 3px 3px 5px -3px rgba(127, 53, 178, 1);
    -moz-box-shadow: 3px 3px 5px -3px rgba(127, 53, 178, 1);
    box-shadow: 3px 3px 5px -3px rgba(127, 53, 178, 1);
}

.info-box {

    max-width: 100%;
    font-size: 12px;
    padding: 10px;
}

/* Triangle styles */
.triangle-container::before {
    content: '';
    position: absolute;
    top: 8px;
    right: 100%;
    border-width: 7px;
    border-style: solid;
    border-color: transparent #fff transparent transparent;

}


input[type="number"] {
    -moz-appearance: textfield;
}

/* For WebKit-based browsers (Chrome, Safari) */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}


@media (max-width: 1200px) {
    .triangle-container {
        right: 0px;
    }
}

/* DASHBOARD */


/* LOGIN */

.popup_content {
    overflow-y: auto;
}

/* LOGIN */

/* PAYMENT */

.negative-balance {
    color: red;
}

#sepet_scroll_part::-webkit-scrollbar {
    display: none;
}

#sepet_scroll_part {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
}


/* PAYMENT */


/* SIDE_BENEFIT_APPROVAL */

.kvvk_popup::-webkit-scrollbar {
    display: none;
}

.kvvk_popup {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.side_benefit_approval strong {
    color: #7F35B2 !important;
}

/* SIDE_BENEFIT_APPROVAL */


/* CLOSED_PAGE_BODY */
.closed_page_body {
    height: 100%;
    margin: 0;
}

.closed_page_body {
    background-image: url('{{ asset("storage/" . $siteMovementData->image) }}');
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

/* CLOSED_PAGE_BODY */

/* ESNEK HAK */

#esnekA a {
    color: #7F35B2 !important;
    font-weight: 600;
}


/* ESNEK HAK */


/* SEÇENEKLİ HAK */


#secenekliA a {
    color: #7F35B2 !important;
}

#secenekliA strong {
    color: #7F35B2 !important;
}

/* SEÇENEKLİ HAK */

.red {
    color: red !important
}

.green {
    color: green !important;
}

.bg-red {
    background-color: #ff000038 !important
}

.bg-green {
    background-color: #00800038 !important;
}

.null {
    color: #7F35B2 !important;
}

.font-medium {
    font-family: "Poppins-Medium", sans-serif;
}

.font-semibold {
    font-family: "Poppins-SemiBold", sans-serif;
}

.font-bold {
    font-family: "Poppins-Bold", sans-serif;
}
